# تعليمات تشغيل تطبيق الانتخابات اللامركزي

## المتطلبات الأساسية

قبل البدء في تشغيل التطبيق، تأكد من توفر المتطلبات التالية:

1. **Node.js** (الإصدار 14 أو أحدث)
2. **npm** (مدير حزم Node.js)
3. **MetaMask** (امتداد متصفح للتفاعل مع تطبيقات Ethereum)
4. **Hardhat** (بيئة تطوير Ethereum محلية)

## خطوات التثبيت

### 1. استنساخ المشروع

```bash
git clone https://github.com/yourusername/election-dapp.git
cd election-dapp
```

### 2. تثبيت التبعيات

```bash
# تثبيت تبعيات العقد الذكي
npm install

# تثبيت تبعيات الواجهة الأمامية
cd election-dapp-frontend
npm install
cd ..
```

### 3. تشغيل بيئة Ethereum المحلية

```bash
# تشغيل شبكة Hardhat المحلية
npx hardhat node
```

### 4. نشر العقد الذكي

في نافذة طرفية جديدة:

```bash
# نشر العقد الذكي على الشبكة المحلية
npx hardhat run scripts/deploy.ts --network localhost
```

احتفظ بعنوان العقد المنشور الذي سيظهر في المخرجات.

### 5. تكوين الواجهة الأمامية

قم بتحديث عنوان العقد في ملف `election-dapp-frontend/src/utils/contractService.ts`:

```typescript
// عنوان العقد الذكي المنشور
const CONTRACT_ADDRESS = 'عنوان_العقد_المنشور_هنا';
```

### 6. تشغيل الواجهة الأمامية

```bash
cd election-dapp-frontend
npm start
```

سيتم فتح التطبيق تلقائيًا في المتصفح على العنوان `http://localhost:3000`.

## استخدام التطبيق

### إعداد MetaMask

1. قم بتثبيت امتداد MetaMask في متصفحك
2. قم بإنشاء محفظة جديدة أو استيراد محفظة موجودة
3. قم بتوصيل MetaMask بشبكة Hardhat المحلية:
   - اسم الشبكة: Hardhat
   - عنوان URL: http://127.0.0.1:8545
   - معرف السلسلة: 31337
   - رمز العملة: ETH
4. قم باستيراد بعض الحسابات من شبكة Hardhat المحلية باستخدام المفاتيح الخاصة التي تظهر عند تشغيل الشبكة

### دليل المستخدم

#### للناخبين:

1. **تسجيل الناخب**:
   - انقر على زر "تسجيل / دخول" في الشريط العلوي
   - انتقل إلى علامة التبويب "تسجيل ناخب جديد"
   - قم بتوصيل محفظتك باستخدام زر "الاتصال بالمحفظة أولاً"
   - أدخل الرقم الوطني المكون من 11 رقم
   - انقر على زر "تسجيل"

2. **التصويت**:
   - انتقل إلى صفحة "الانتخابات" من القائمة العلوية
   - اختر الانتخابات النشطة التي ترغب في المشاركة فيها
   - استعرض المرشحين وصورهم ومعلوماتهم
   - انقر على زر "تصويت" بجانب المرشح الذي تريد التصويت له
   - قم بتأكيد المعاملة في MetaMask

3. **عرض النتائج**:
   - انتقل إلى صفحة "النتائج" من القائمة العلوية
   - اختر الانتخابات التي ترغب في عرض نتائجها من القائمة المنسدلة
   - استعرض الرسومات البيانية وتفاصيل المرشحين ونتائجهم

#### للمسؤول:

1. **الوصول إلى لوحة التحكم**:
   - قم بتسجيل الدخول باستخدام حساب المالك (الحساب الذي نشر العقد)
   - انتقل إلى `/admin` في المتصفح

2. **إنشاء انتخابات جديدة**:
   - في لوحة التحكم، انتقل إلى علامة التبويب "إدارة الانتخابات"
   - انقر على زر "إنشاء انتخابات جديدة"
   - أدخل عنوان الانتخابات وتاريخ ووقت البدء والانتهاء
   - انقر على زر "إنشاء"

3. **إدارة المرشحين**:
   - في لوحة التحكم، انتقل إلى علامة التبويب "إدارة المرشحين"
   - انقر على زر "إضافة مرشح جديد"
   - اختر الانتخابات التي ترغب في إضافة المرشح إليها
   - أدخل اسم المرشح
   - قم برفع صورة المرشح إلى IPFS باستخدام زر "اختيار ملف" ثم "رفع الملف إلى IPFS"
   - انقر على زر "إضافة"

4. **إدارة الانتخابات**:
   - في لوحة التحكم، يمكنك إيقاف أو استئناف الانتخابات باستخدام الأزرار المناسبة
   - يمكنك أيضًا عرض تفاصيل الانتخابات والمرشحين والناخبين

## استكشاف الأخطاء وإصلاحها

### مشاكل الاتصال بالمحفظة

- تأكد من تثبيت MetaMask وتكوينه بشكل صحيح
- تأكد من اتصال MetaMask بشبكة Hardhat المحلية
- تأكد من وجود رصيد ETH كافٍ في حسابك لدفع رسوم المعاملات

### مشاكل التصويت

- تأكد من تسجيلك كناخب في الانتخابات المعنية
- تأكد من أن الانتخابات نشطة (بدأت ولم تنتهِ وغير متوقفة)
- تأكد من أنك لم تصوت بالفعل في هذه الانتخابات

### مشاكل IPFS

- إذا لم تظهر صور المرشحين، تأكد من اتصالك بالإنترنت
- قد تستغرق صور IPFS بعض الوقت للتحميل، انتظر قليلاً

## اختبار التطبيق

يمكنك اختبار العقد الذكي باستخدام الأمر التالي:

```bash
npx hardhat test
```

## الاتصال بشبكة Ethereum الرئيسية

لنشر التطبيق على شبكة Ethereum الرئيسية أو شبكات الاختبار العامة:

1. قم بتعديل ملف `hardhat.config.ts` لإضافة تكوين الشبكة المطلوبة
2. قم بإنشاء ملف `.env` وإضافة المفتاح الخاص ومفاتيح API الضرورية
3. قم بتشغيل أمر النشر مع تحديد الشبكة المطلوبة:

```bash
npx hardhat run scripts/deploy.ts --network ropsten
```

4. قم بتحديث عنوان العقد في الواجهة الأمامية وإعادة بناء التطبيق

## ملاحظات هامة

- هذا التطبيق مصمم للأغراض التعليمية والتجريبية فقط
- في بيئة الإنتاج، يجب اتخاذ إجراءات أمان إضافية
- تأكد من اختبار التطبيق بشكل شامل قبل استخدامه في سيناريوهات حقيقية
