// تعريف واجهة الرسالة
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// تعريف واجهة استجابة Google Gemini
interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}

// الإعدادات الافتراضية للنموذج
const DEFAULT_MODEL = 'gemini-pro';  // نموذج Google Gemini
const DEFAULT_TEMPERATURE = 0.7;
const DEFAULT_MAX_TOKENS = 1000;

// معلومات حول الانتخابات للسياق
const ELECTION_CONTEXT = `
أنت مساعد ذكي للناخبين في نظام التصويت الإلكتروني. مهمتك هي مساعدة الناخبين خلال العملية الانتخابية وتوجيههم.
معلومات مهمة عن النظام:
1. يجب على الناخب التسجيل أولاً قبل التصويت
2. يمكن للناخب التصويت مرة واحدة فقط في كل انتخابات
3. يمكن للناخب عرض المرشحين ومعلوماتهم قبل التصويت
4. نتائج الانتخابات تظهر فقط بعد انتهاء فترة التصويت
5. يجب على الناخب توصيل محفظته الإلكترونية للتصويت

قدم إجابات مختصرة ومفيدة، وكن ودودًا ومهذبًا في تعاملك مع الناخبين.
`;

/**
 * خدمة للتواصل مع Google Gemini API
 */
class ChatService {
  private apiKey: string;
  private apiUrl: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;

  constructor() {
    // استخدام متغيرات البيئة للحصول على مفتاح API
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    // عنوان URL لـ Google Gemini API
    this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
    this.model = DEFAULT_MODEL;
    this.temperature = DEFAULT_TEMPERATURE;
    this.maxTokens = DEFAULT_MAX_TOKENS;

    // طباعة معلومات التهيئة للتصحيح
    console.log('Google Gemini Service initialized with API key:', this.apiKey ? 'Present' : 'Missing');
    console.log('Gemini API URL:', this.apiUrl);
    console.log('Gemini Model:', this.model);
  }

  /**
   * إرسال رسالة إلى ChatGPT API والحصول على استجابة
   * @param messages سجل المحادثة
   * @returns وعد بالاستجابة من ChatGPT
   */
  async sendMessage(messages: Message[]): Promise<string> {
    try {
      // التحقق من وجود مفتاح API
      if (!this.apiKey || this.apiKey === '') {
        throw new Error('مفتاح Google Gemini API غير متوفر. يرجى إضافة VITE_GEMINI_API_KEY في ملف .env');
      }

      console.log('Using real Google Gemini API with key:', this.apiKey.substring(0, 5) + '...');

      // تحويل الرسائل إلى تنسيق Google Gemini
      const conversationText = messages.map(msg => {
        const role = msg.role === 'user' ? 'User' : 'Assistant';
        return `${role}: ${msg.content}`;
      }).join('\n');

      const fullPrompt = `${ELECTION_CONTEXT}\n\nالمحادثة:\n${conversationText}\nAssistant:`;

      // إعداد طلب API
      console.log('Sending request to Google Gemini API...');

      try {
        // إرسال الطلب إلى Google Gemini API
        const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: fullPrompt
              }]
            }],
            generationConfig: {
              temperature: this.temperature,
              maxOutputTokens: this.maxTokens
            }
          })
        });

        // التحقق من نجاح الطلب
        if (!response.ok) {
          console.error('API error:', response.status, response.statusText);

          let errorMessage = 'حدث خطأ في الاتصال بخدمة الذكاء الاصطناعي';

          // تخصيص رسائل الخطأ بناءً على رمز الحالة
          switch (response.status) {
            case 401:
              errorMessage = 'مفتاح API غير صحيح أو منتهي الصلاحية';
              break;
            case 402:
              errorMessage = 'الرصيد غير كافٍ في الحساب. يرجى إضافة رصيد أو التحقق من الفوترة';
              break;
            case 429:
              errorMessage = 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً';
              break;
            case 500:
              errorMessage = 'خطأ في خادم Google Gemini. يرجى المحاولة لاحقاً';
              break;
            case 503:
              errorMessage = 'خدمة Google Gemini غير متاحة حالياً. يرجى المحاولة لاحقاً';
              break;
            default:
              errorMessage = `خطأ في API: ${response.status} ${response.statusText}`;
          }

          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await response.json();
            console.error('API error details:', errorData);

            // إذا كان هناك رسالة خطأ محددة من API، استخدمها
            if (errorData.error && errorData.error.message) {
              errorMessage += `: ${errorData.error.message}`;
            }
          } catch (e) {
            console.error('Could not parse error response');
          }

          throw new Error(errorMessage);
        }

        // تحويل الاستجابة إلى JSON
        const data: GeminiResponse = await response.json();
        console.log('Google Gemini API response received');

        // التحقق من وجود الاستجابة
        if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
          // استخراج محتوى الرسالة من الاستجابة
          return data.candidates[0].content.parts[0].text;
        } else {
          console.error('Invalid API response format:', data);
          throw new Error('تنسيق استجابة API غير صحيح');
        }
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        throw fetchError;
      }
    } catch (error) {
      console.error('Error in chatbot:', error);
      throw error;
    }
  }



  /**
   * تعيين نموذج ChatGPT
   * @param model اسم النموذج
   */
  setModel(model: string): void {
    this.model = model;
  }

  /**
   * تعيين درجة حرارة النموذج (التنوع في الإجابات)
   * @param temperature قيمة بين 0 و 1
   */
  setTemperature(temperature: number): void {
    this.temperature = temperature;
  }

  /**
   * تعيين الحد الأقصى للرموز في الاستجابة
   * @param maxTokens عدد الرموز
   */
  setMaxTokens(maxTokens: number): void {
    this.maxTokens = maxTokens;
  }
}

// تصدير نسخة واحدة من الخدمة (Singleton)
export const chatService = new ChatService();
