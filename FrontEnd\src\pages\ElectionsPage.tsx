import React, { useState } from 'react';
import { ElectionsList, ElectionDetails, ElectionResults } from '../components/elections';
import { ChevronRight } from 'lucide-react';

export const ElectionsPage: React.FC = () => {
  const [selectedElectionId, setSelectedElectionId] = useState<number | null>(null);
  const [view, setView] = useState<'list' | 'details' | 'results'>('list');
  
  const handleSelectElection = (electionId: number) => {
    setSelectedElectionId(electionId);
    setView('details');
  };
  
  const handleBackToList = () => {
    setView('list');
    setSelectedElectionId(null);
  };
  
  const handleViewResults = () => {
    if (selectedElectionId !== null) {
      setView('results');
    }
  };
  
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        {view !== 'list' && (
          <div className="flex items-center mb-4">
            <button
              onClick={handleBackToList}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronRight className="w-5 h-5 ml-1" />
              العودة إلى قائمة الانتخابات
            </button>
          </div>
        )}
        
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">
            {view === 'list'
              ? 'الانتخابات المتاحة'
              : view === 'details'
              ? 'تفاصيل الانتخابات'
              : 'نتائج الانتخابات'}
          </h1>
          
          {view === 'details' && selectedElectionId !== null && (
            <button
              onClick={handleViewResults}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150"
            >
              عرض النتائج
            </button>
          )}
          
          {view === 'results' && selectedElectionId !== null && (
            <button
              onClick={() => setView('details')}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150"
            >
              العودة إلى التفاصيل
            </button>
          )}
        </div>
      </div>
      
      {view === 'list' && (
        <ElectionsList onSelectElection={handleSelectElection} />
      )}
      
      {view === 'details' && selectedElectionId !== null && (
        <ElectionDetails electionId={selectedElectionId} />
      )}
      
      {view === 'results' && selectedElectionId !== null && (
        <ElectionResults electionId={selectedElectionId} />
      )}
    </div>
  );
};
