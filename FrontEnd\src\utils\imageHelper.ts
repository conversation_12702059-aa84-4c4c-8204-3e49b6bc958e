/**
 * وظائف مساعدة للتعامل مع الصور
 */

/**
 * تحويل رابط الصورة إلى رابط قابل للعرض
 * @param imageUrl رابط الصورة (قد يكون رابط IPFS أو Data URL أو رابط HTTP)
 * @returns رابط قابل للعرض
 */
export const getDisplayableImageUrl = (imageUrl: string | null | undefined): string => {
  if (!imageUrl) {
    console.log('Image URL is null or undefined');
    return 'https://via.placeholder.com/300x200?text=صورة+المرشح';
  }

  console.log('Processing image URL:', imageUrl.substring(0, 30), '...');

  // إذا كان الرابط يبدأ بـ data: فهو Data URL
  if (imageUrl.startsWith('data:')) {
    console.log('Detected as Data URL');
    return imageUrl;
  }

  // إذا كان الرابط يبدأ بـ ipfs:// فهو رابط IPFS
  if (imageUrl.startsWith('ipfs://')) {
    console.log('Detected as IPFS URL');
    const cid = imageUrl.replace('ipfs://', '');
    return `https://ipfs.thirdwebcdn.com/ipfs/${cid}`;
  }

  // إذا كان الرابط يبدأ بـ http:// أو https:// فهو رابط HTTP
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    console.log('Detected as HTTP URL');
    return imageUrl;
  }

  // محاولة إصلاح الرابط إذا كان مقطوعاً
  if (imageUrl.includes('data:image/')) {
    console.log('Attempting to fix truncated Data URL');
    // إذا كان الرابط يحتوي على data:image/ ولكن لا يبدأ به
    const dataUrlIndex = imageUrl.indexOf('data:image/');
    if (dataUrlIndex >= 0) {
      return imageUrl.substring(dataUrlIndex);
    }
  }

  // إذا لم يكن أي من الأنواع المعروفة، نعيد رابط الصورة الافتراضية
  console.log('Unknown URL format, using placeholder');
  return 'https://via.placeholder.com/300x200?text=صورة+المرشح';
};

/**
 * التحقق من صحة رابط الصورة
 * @param imageUrl رابط الصورة
 * @returns true إذا كان الرابط صالحًا، false إذا لم يكن كذلك
 */
export const isValidImageUrl = (imageUrl: string | null | undefined): boolean => {
  if (!imageUrl) {
    console.log('isValidImageUrl: URL is null or undefined');
    return false;
  }

  // التحقق من الروابط المعروفة
  if (
    imageUrl.startsWith('data:image/') ||
    imageUrl.startsWith('ipfs://') ||
    imageUrl.startsWith('http://') ||
    imageUrl.startsWith('https://')
  ) {
    return true;
  }

  // التحقق من الروابط المقطوعة
  if (imageUrl.includes('data:image/')) {
    console.log('isValidImageUrl: Found truncated Data URL');
    return true;
  }

  console.log('isValidImageUrl: Invalid URL format');
  return false;
};

export default {
  getDisplayableImageUrl,
  isValidImageUrl
};
