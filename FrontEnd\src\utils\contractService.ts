import { ethers } from 'ethers';
import ElectionABI from '../../../artifacts/contracts//Election.sol/Election.json';
import {
  EventCallback,
  ElectionCreatedEvent,
  VoteCastEvent,
  CandidateAddedEvent,
  ElectionPausedEvent,
  ElectionResumedEvent,
  AdminChangedEvent,
  VoterRegisteredEvent
} from '../interfaces';

// Define types for window.ethereum
declare global {
  interface Window {
    ethereum?: any;
  }
}

// Contract address
const CONTRACT_ADDRESS = import.meta.env.VITE_CONTRACT_ADDRESS;

// Types
interface Election {
  id: number;
  title: string;
  startTime: number;
  endTime: number;
  isPaused: boolean;
  candidateCount: number;
  candidates: Candidate[];
}

interface Candidate {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  voteCount: number;
}

export interface Voter {
  address: string;
  nationalId: string;
  hasVoted: boolean;
}

// Create Ethereum provider
const getProvider = (): ethers.providers.Web3Provider => {
  if (window.ethereum) {
    return new ethers.providers.Web3Provider(window.ethereum);
  }
  throw new Error('Please install MetaMask or a Web3 compatible browser');
};

// Get contract instance
const getContract = (withSigner = false): ethers.Contract => {
  const provider = getProvider();
  const contract = new ethers.Contract(CONTRACT_ADDRESS, ElectionABI.abi, provider);

  if (withSigner) {
    const signer = provider.getSigner();
    return contract.connect(signer);
  }

  return contract;
};

// Connect wallet
export const connectWallet = async (): Promise<{ address: string; isAdmin: boolean }> => {
  try {
    if (!window.ethereum) {
      throw new Error('MetaMask is not installed. Please install MetaMask to use this application.');
    }

    // Request accounts and wait for user approval
    await window.ethereum.request({ method: 'eth_requestAccounts' });

    const provider = getProvider();
    const signer = provider.getSigner();
    const address = await signer.getAddress();

    if (!address) {
      throw new Error('No account selected. Please select an account in MetaMask.');
    }

    // Get contract instance
    const contract = getContract();

    // Check if user is admin
    const adminAddress = await contract.admin();
    const isAdmin = adminAddress.toLowerCase() === address.toLowerCase();

    console.log('Wallet Connection Result:', {
      address,
      adminAddress,
      isAdmin
    });

    return { address, isAdmin };
  } catch (error) {
    console.error('Wallet connection error:', error);
    throw new Error(
      error instanceof Error
        ? error.message
        : 'Failed to connect wallet. Please ensure MetaMask is installed and grant access.'
    );
  }
};

// Create new election
export const createElection = async (title: string, startTime: number, endTime: number): Promise<number> => {
  try {
    const contract = getContract(true); // تأكد من استخدام العقد مع signer

    // تحقق من صحة المدخلات
    if (!title || startTime <= 0 || endTime <= 0) {
      throw new Error('Invalid input parameters');
    }

    // إرسال المعاملة
    const tx = await contract.createElection(title, startTime, endTime);
    console.log('Transaction sent:', tx.hash);

    // انتظار تأكيد المعاملة
    const receipt = await tx.wait(1); // انتظار block واحد للتأكيد
    console.log('Transaction confirmed:', receipt);

    // التحقق من الحدث
    const event = receipt.events?.find((e: { event: string }) => e.event === 'ElectionCreated');
    if (!event) {
      throw new Error('Election creation failed: Event not emitted');
    }

    // تسجيل نجاح العملية
    const electionId = event.args?.electionId.toNumber();
    console.log('Election created successfully:', {
      electionId,
      title: event.args?.title
    });

    return electionId;
  } catch (error: any) {
    console.error('Error in createElection:', error);
    if (error.code === 'ACTION_REJECTED') {
      throw new Error('Transaction was rejected by user');
    } else if (error.message.includes('user rejected')) {
      throw new Error('Transaction was rejected by user');
    } else {
      throw new Error(error.message || 'Failed to create election');
    }
  }
};

// Add new candidate
export const addCandidate = async (
  electionId: number,
  candidateId: number,
  name: string,
  description: string,
  imageUrl: string
): Promise<boolean> => {
  try {
    console.log('Adding candidate with the following details:');
    console.log('- Election ID:', electionId);
    console.log('- Candidate ID:', candidateId);
    console.log('- Name:', name);
    console.log('- Description:', description);
    console.log('- Image URL length:', imageUrl.length, 'characters');
    console.log('- Image URL format:', imageUrl.startsWith('data:image/webp') ? 'webp' :
                                    imageUrl.startsWith('data:image/jpeg') ? 'jpeg' :
                                    imageUrl.startsWith('data:image/png') ? 'png' : 'unknown');
    console.log('- Image URL starts with:', imageUrl.substring(0, 30), '...');

    // التحقق من صحة المدخلات
    if (isNaN(electionId) || electionId < 0) {
      throw new Error('معرف الانتخابات غير صالح');
    }

    if (isNaN(candidateId) || candidateId < 0) {
      throw new Error('معرف المرشح غير صالح');
    }

    if (!name || name.trim() === '') {
      throw new Error('اسم المرشح مطلوب');
    }

    if (!imageUrl) {
      throw new Error('رابط صورة المرشح مطلوب');
    }

    // التحقق من حجم رابط الصورة
    if (imageUrl.length > 1000000) { // أكثر من 1 ميجابايت
      throw new Error('حجم رابط الصورة كبير جداً');
    }

    const contract = getContract(true);
    console.log('Sending transaction to add candidate...');
    console.log('Gas estimation starting...');

    try {
      // تقدير الغاز المطلوب للمعاملة
      const gasEstimate = await contract.estimateGas.addCandidate(electionId, candidateId, name, description, imageUrl);
      console.log('Gas estimation successful. Estimated gas:', gasEstimate.toString());

      // إضافة معلومات الغاز إلى المعاملة
      const tx = await contract.addCandidate(electionId, candidateId, name, description, imageUrl, {
        gasLimit: gasEstimate.mul(120).div(100) // إضافة 20% إلى تقدير الغاز
      });

      console.log('Transaction sent, hash:', tx.hash);
      console.log('Waiting for confirmation...');

      const receipt = await tx.wait();
      console.log('Transaction confirmed:', receipt.transactionHash);
      console.log('Gas used:', receipt.gasUsed.toString());
    } catch (gasError: any) {
      console.error('Gas estimation or transaction error:', gasError);

      if (gasError.message.includes('exceeds block gas limit') ||
          gasError.message.includes('intrinsic gas too low')) {
        console.error('Gas limit exceeded. Image size in KB:', Math.round(imageUrl.length / 1024));
        throw new Error('حجم البيانات كبير جداً للمعاملة. يرجى تقليل حجم الصورة أو استخدام صورة أخرى.');
      }

      throw gasError;
    }
    return true;
  } catch (error: any) {
    console.error('Error adding candidate:', error);

    // التحقق من نوع الخطأ
    if (error.code === 'ACTION_REJECTED') {
      throw new Error('تم رفض المعاملة من قبل المستخدم');
    } else if (error.message.includes('user rejected')) {
      throw new Error('تم رفض المعاملة من قبل المستخدم');
    } else if (error.message.includes('Election does not exist')) {
      throw new Error('الانتخابات غير موجودة');
    } else if (error.message.includes('Candidate ID already exists')) {
      throw new Error('معرف المرشح موجود بالفعل');
    } else if (error.message.includes('Election has already started')) {
      throw new Error('لقد بدأت الانتخابات بالفعل، لا يمكن إضافة مرشح');
    } else if (error.message.includes('Only admin')) {
      throw new Error('يجب أن تكون مسؤولاً للقيام بهذه العملية');
    }

    throw new Error(error.message || 'فشل إضافة المرشح');
  }
};

// Vote for candidate
export const vote = async (
  electionId: number,
  candidateId: number
): Promise<boolean> => {
  try {
    console.log(`Voting for candidate ${candidateId} in election ${electionId}`);

    // التحقق من صحة المدخلات
    if (isNaN(electionId) || electionId < 0) {
      throw new Error('معرف الانتخابات غير صالح');
    }

    if (isNaN(candidateId) || candidateId < 0) {
      throw new Error('معرف المرشح غير صالح');
    }

    const contract = getContract(true);

    // التحقق من وجود المرشح قبل التصويت
    try {
      const election = await contract.elections(electionId);
      console.log(`Election ${electionId} details:`, {
        title: election.title,
        startTime: election.startTime.toNumber(),
        endTime: election.endTime.toNumber(),
        isPaused: election.isPaused
      });

      // التحقق من وجود المرشح
      try {
        const results = await contract.getElectionResults(electionId);
        console.log(`Found ${results.length} candidates in election ${electionId}`);

        if (candidateId >= results.length) {
          console.error(`Candidate ID ${candidateId} is out of range. Max index is ${results.length - 1}`);
          throw new Error('المرشح غير موجود');
        }

        console.log(`Candidate ${candidateId} details:`, {
          name: results[candidateId].name,
          voteCount: results[candidateId].voteCount.toNumber()
        });
      } catch (candidateErr) {
        console.error('Error checking candidate existence:', candidateErr);
        if (candidateErr.message.includes('المرشح غير موجود')) {
          throw candidateErr;
        }
      }
    } catch (err) {
      console.error('Error fetching election details:', err);
      if (err.message.includes('المرشح غير موجود')) {
        throw err;
      }
    }

    console.log('Sending vote transaction...');
    const tx = await contract.vote(electionId, candidateId);
    console.log('Vote transaction sent, waiting for confirmation...');
    const receipt = await tx.wait();
    console.log('Vote transaction confirmed:', receipt.transactionHash);
    return true;
  } catch (error: unknown) {
    console.error('Voting error:', error);
    if (error instanceof Error) {
      if (error.message.includes('already voted')) {
        throw new Error('You have already voted in this election');
      } else if (error.message.includes('not registered')) {
        throw new Error('You are not registered as a voter in this election');
      } else if (error.message.includes('paused')) {
        throw new Error('The election is currently paused');
      } else if (error.message.includes('not started')) {
        throw new Error('The election has not started yet');
      } else if (error.message.includes('ended')) {
        throw new Error('The election has ended');
      } else if (error.message.includes('Candidate does not exist')) {
        throw new Error('المرشح غير موجود');
      }
    }
    throw new Error('Failed to vote');
  }
};

// Self register voter
export const selfRegisterVoter = async (
  electionId: number,
  nationalId: number
): Promise<boolean> => {
  try {
    console.log(`contractService: Self-registering voter for election ${electionId} with national ID ${nationalId}`);

    // التحقق من صحة المدخلات
    if (isNaN(electionId) || electionId < 0) {
      console.error('Invalid election ID:', electionId);
      throw new Error('معرف الانتخابات غير صالح');
    }

    if (isNaN(nationalId) || nationalId <= 0) {
      console.error('Invalid national ID:', nationalId);
      throw new Error('الرقم الوطني غير صالح');
    }

    const contract = getContract(true);

    // التحقق من التسجيل المسبق
    try {
      const isRegistered = await contract.isVoterRegistered(electionId, await contract.signer.getAddress());
      if (isRegistered) {
        console.log('Voter is already registered');
        return true; // الناخب مسجل بالفعل
      }
    } catch (checkErr) {
      console.warn('Error checking registration status before self-registration:', checkErr);
      // استمر في محاولة التسجيل
    }

    // إرسال المعاملة
    console.log('Sending self-registration transaction...');
    const tx = await contract.selfRegister(electionId, nationalId);
    console.log('Transaction sent, waiting for confirmation...');

    // انتظار تأكيد المعاملة
    const receipt = await tx.wait();
    console.log('Self-registration transaction confirmed:', receipt.transactionHash);

    return true;
  } catch (error: unknown) {
    console.error('Voter self-registration error:', error);

    // التحقق من نوع الخطأ
    if (error instanceof Error) {
      if (error.message.includes('already registered')) {
        return true; // الناخب مسجل بالفعل
      } else if (error.message.includes('user rejected transaction')) {
        throw new Error('تم رفض المعاملة من قبل المستخدم');
      } else if (error.message.includes('Election does not exist')) {
        throw new Error('الانتخابات غير موجودة');
      } else if (error.message.includes('Election has already started')) {
        throw new Error('لقد بدأت الانتخابات بالفعل، لا يمكن التسجيل');
      }
    }

    throw new Error('فشل تسجيل الناخب');
  }
};

// Admin register voter
export const registerVoter = async (
  electionId: number,
  voterAddress: string,
  nationalId: number
): Promise<boolean> => {
  try {
    console.log(`contractService: Admin registering voter ${voterAddress} for election ${electionId} with national ID ${nationalId}`);

    // التحقق من صحة المدخلات
    if (isNaN(electionId) || electionId < 0) {
      console.error('Invalid election ID:', electionId);
      throw new Error('معرف الانتخابات غير صالح');
    }

    if (!voterAddress || voterAddress === '0x0000000000000000000000000000000000000000') {
      console.error('Invalid voter address:', voterAddress);
      throw new Error('عنوان الناخب غير صالح');
    }

    if (isNaN(nationalId) || nationalId <= 0) {
      console.error('Invalid national ID:', nationalId);
      throw new Error('الرقم الوطني غير صالح');
    }

    const contract = getContract(true);

    // التحقق من التسجيل المسبق
    try {
      const isRegistered = await contract.isVoterRegistered(electionId, voterAddress);
      if (isRegistered) {
        console.log('Voter is already registered');
        return true; // الناخب مسجل بالفعل
      }
    } catch (checkErr) {
      console.warn('Error checking registration status before admin registration:', checkErr);
      // استمر في محاولة التسجيل
    }

    // إرسال المعاملة
    console.log('Sending admin registration transaction...');
    const tx = await contract.registerVoter(electionId, voterAddress, nationalId);
    console.log('Transaction sent, waiting for confirmation...');

    // انتظار تأكيد المعاملة
    const receipt = await tx.wait();
    console.log('Admin registration transaction confirmed:', receipt.transactionHash);

    return true;
  } catch (error: unknown) {
    console.error('Voter registration error:', error);

    // التحقق من نوع الخطأ
    if (error instanceof Error) {
      if (error.message.includes('already registered')) {
        return true; // الناخب مسجل بالفعل
      } else if (error.message.includes('user rejected transaction')) {
        throw new Error('تم رفض المعاملة من قبل المستخدم');
      } else if (error.message.includes('Election does not exist')) {
        throw new Error('الانتخابات غير موجودة');
      } else if (error.message.includes('Election has already started')) {
        throw new Error('لقد بدأت الانتخابات بالفعل، لا يمكن التسجيل');
      } else if (error.message.includes('Only admin')) {
        throw new Error('يجب أن تكون مسؤولاً للقيام بهذه العملية');
      }
    }

    throw new Error('فشل تسجيل الناخب');
  }
};

// Pause election
export const pauseElection = async (electionId: number): Promise<boolean> => {
  try {
    const contract = getContract(true);
    const tx = await contract.pauseElection(electionId);
    await tx.wait();
    return true;
  } catch (error) {
    console.error('Error pausing election:', error);
    throw new Error('Failed to pause election');
  }
};

// Resume election
export const resumeElection = async (electionId: number): Promise<boolean> => {
  try {
    const contract = getContract(true);
    const tx = await contract.resumeElection(electionId);
    await tx.wait();
    return true;
  } catch (error) {
    console.error('Error resuming election:', error);
    throw new Error('Failed to resume election');
  }
};

// Check if address is registered as voter
export const isVoterRegistered = async (electionId: number, voterAddress: string): Promise<boolean> => {
  try {
    console.log(`contractService: Checking if voter ${voterAddress} is registered for election ${electionId}`);

    // التحقق من صحة المدخلات
    if (!voterAddress || voterAddress === '0x0000000000000000000000000000000000000000') {
      console.error('Invalid voter address:', voterAddress);
      return false;
    }

    if (isNaN(electionId) || electionId < 0) {
      console.error('Invalid election ID:', electionId);
      return false;
    }

    const contract = getContract();

    // استخدام مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
    const timeoutPromise = new Promise<boolean>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout checking voter registration')), 5000);
    });

    // استدعاء العقد الذكي
    const contractPromise = contract.isVoterRegistered(electionId, voterAddress);

    // استخدام Promise.race لتحديد مهلة زمنية
    const isRegistered = await Promise.race([contractPromise, timeoutPromise]);

    console.log(`contractService: Voter ${voterAddress} registration status for election ${electionId}: ${isRegistered}`);
    return isRegistered;
  } catch (error) {
    console.error('Error checking voter registration:', error);
    // إرجاع false بدلاً من رمي خطأ لتجنب تعطل التطبيق
    return false;
  }
};

// Check if voter has already voted
export const hasVoted = async (electionId: number, voterAddress: string): Promise<boolean> => {
  try {
    console.log(`contractService: Checking if voter ${voterAddress} has voted in election ${electionId}`);

    // التحقق من صحة المدخلات
    if (!voterAddress || voterAddress === '0x0000000000000000000000000000000000000000') {
      console.error('Invalid voter address:', voterAddress);
      return false;
    }

    if (isNaN(electionId) || electionId < 0) {
      console.error('Invalid election ID:', electionId);
      return false;
    }

    const contract = getContract();

    // استخدام مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
    const timeoutPromise = new Promise<boolean>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout checking if voter has voted')), 5000);
    });

    // استدعاء العقد الذكي
    const contractPromise = contract.hasVoted(electionId, voterAddress);

    // استخدام Promise.race لتحديد مهلة زمنية
    const hasVoted = await Promise.race([contractPromise, timeoutPromise]);

    console.log(`contractService: Voter ${voterAddress} has voted in election ${electionId}: ${hasVoted}`);
    return hasVoted;
  } catch (error) {
    console.error('Error checking if voter has voted:', error);
    // إرجاع false بدلاً من رمي خطأ لتجنب تعطل التطبيق
    return false;
  }
};

// Get elections list
export const getElections = async (): Promise<Election[]> => {
  try {
    const contract = getContract();
    const elections: Election[] = [];

    // Get the total number of elections
    const electionCount = await contract.electionCount();
    console.log('Total elections count:', electionCount.toNumber());

    // Loop through all elections
    for (let i = 0; i < electionCount.toNumber(); i++) {
      try {
        // Get election details
        const election = await contract.elections(i);
        const candidates = await getElectionCandidates(i);

        console.log(`Election ${i} details:`, {
          title: election.title,
          startTime: election.startTime.toNumber(),
          endTime: election.endTime.toNumber(),
          isPaused: election.isPaused
        });

        elections.push({
          id: i,
          title: election.title,
          startTime: election.startTime.toNumber(),
          endTime: election.endTime.toNumber(),
          isPaused: election.isPaused,
          candidateCount: candidates.length,
          candidates
        });
      } catch (error) {
        console.error(`Error fetching election ${i}:`, error);
      }
    }

    console.log('Fetched elections:', elections);
    return elections;
  } catch (error) {
    console.error('Error fetching elections:', error);
    throw new Error('Failed to fetch elections list');
  }
};

// Get election results
export const getElectionResults = async (electionId: number): Promise<Candidate[]> => {
  try {
    const contract = getContract();
    const results = await contract.getElectionResults(electionId);

    // طباعة معلومات مفصلة عن المرشحين
    console.log('Raw candidates data from contract:', results);

    // استخدام candidateId الفعلي بدلاً من index
    const candidates = results.map((candidate: any, index: number) => {
      console.log(`Candidate ${index} raw data:`, {
        name: candidate.name,
        description: candidate.description,
        imageURL: candidate.imageURL,
        voteCount: candidate.voteCount.toNumber()
      });

      // محاولة استخراج معرف المرشح من البيانات إذا كان متاحًا
      // وإلا استخدام الفهرس كمعرف بديل
      const candidateId = index; // استخدام الفهرس كمعرف افتراضي

      return {
        id: candidateId, // استخدام معرف المرشح الفعلي
        name: candidate.name,
        description: candidate.description,
        imageUrl: candidate.imageURL,
        voteCount: candidate.voteCount.toNumber()
      };
    });

    console.log('Processed candidates:', candidates);
    return candidates;
  } catch (error) {
    console.error('Error fetching election results:', error);
    throw new Error('Failed to fetch election results');
  }
};

// Helper function to get candidates for an election
const getElectionCandidates = async (electionId: number): Promise<Candidate[]> => {
  try {
    const contract = getContract();

    // Try to get election results which returns all candidates
    try {
      const results = await contract.getElectionResults(electionId);
      console.log(`Fetched ${results.length} candidates for election ${electionId}`);

      // طباعة معلومات مفصلة عن المرشحين
      console.log('Raw candidates data from getElectionCandidates:', results);

      // استخدام candidateId الفعلي بدلاً من index
      const candidates = results.map((candidate: any, index: number) => {
        console.log(`Candidate ${index} raw data in getElectionCandidates:`, {
          name: candidate.name,
          description: candidate.description,
          imageURL: candidate.imageURL,
          voteCount: candidate.voteCount.toNumber()
        });

        // محاولة استخراج معرف المرشح من البيانات إذا كان متاحًا
        // وإلا استخدام الفهرس كمعرف بديل
        const candidateId = index; // استخدام الفهرس كمعرف افتراضي

        return {
          id: candidateId, // استخدام معرف المرشح الفعلي
          name: candidate.name,
          description: candidate.description,
          imageUrl: candidate.imageURL,
          voteCount: candidate.voteCount.toNumber()
        };
      });

      console.log('Processed candidates in getElectionCandidates:', candidates);
      return candidates;
    } catch (error) {
      console.log(`No candidates found for election ${electionId} or election doesn't exist yet`);
      return [];
    }
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return [];
  }
};

// Change admin
export const changeAdmin = async (newAdminAddress: string): Promise<boolean> => {
  try {
    const contract = getContract(true);
    const tx = await contract.changeAdmin(newAdminAddress);
    await tx.wait();
    return true;
  } catch (error) {
    console.error('Error changing admin:', error);
    throw new Error('Failed to change admin');
  }
};

// Remove candidate
export const removeCandidate = async (electionId: number, candidateId: number): Promise<boolean> => {
  try {
    const contract = getContract(true);
    const tx = await contract.removeCandidate(electionId, candidateId);
    await tx.wait();
    return true;
  } catch (error) {
    console.error('Error removing candidate:', error);
    throw new Error('Failed to remove candidate');
  }
};

// Update election details
export const updateElectionDetails = async (
  electionId: number,
  title: string,
  startTime: number,
  endTime: number
): Promise<boolean> => {
  try {
    const contract = getContract(true);
    const tx = await contract.updateElectionDetails(electionId, title, startTime, endTime);
    await tx.wait();
    return true;
  } catch (error) {
    console.error('Error updating election details:', error);
    throw new Error('Failed to update election details');
  }
};

// Listen for contract events
export const listenForEvents = (callback: EventCallback) => {
  try {
    const contract = getContract();

    contract.on('ElectionCreated', (electionId, title, startTime, endTime, event) => {
      const eventData: ElectionCreatedEvent = {
        electionId: electionId.toNumber(),
        title,
        startTime: startTime.toNumber(),
        endTime: endTime.toNumber(),
        transactionHash: event.transactionHash
      };
      callback('ElectionCreated', eventData);
    });

    contract.on('VoteCast', (electionId, candidateId, voter, event) => {
      const eventData: VoteCastEvent = {
        electionId: electionId.toNumber(),
        candidateId: candidateId.toNumber(),
        voter,
        transactionHash: event.transactionHash
      };
      callback('VoteCast', eventData);
    });

    contract.on('CandidateAdded', (electionId, candidateId, name, event) => {
      const eventData: CandidateAddedEvent = {
        electionId: electionId.toNumber(),
        candidateId: candidateId.toNumber(),
        name,
        transactionHash: event.transactionHash
      };
      callback('CandidateAdded', eventData);
    });

    contract.on('ElectionPaused', (electionId, event) => {
      const eventData: ElectionPausedEvent = {
        electionId: electionId.toNumber(),
        transactionHash: event.transactionHash
      };
      callback('ElectionPaused', eventData);
    });

    contract.on('ElectionResumed', (electionId, event) => {
      const eventData: ElectionResumedEvent = {
        electionId: electionId.toNumber(),
        transactionHash: event.transactionHash
      };
      callback('ElectionResumed', eventData);
    });

    contract.on('AdminChanged', (oldAdmin, newAdmin, event) => {
      const eventData: AdminChangedEvent = {
        oldAdmin,
        newAdmin,
        transactionHash: event.transactionHash
      };
      callback('AdminChanged', eventData);
    });

    contract.on('VoterRegistered', (electionId, voter, nationalId, event) => {
      const eventData: VoterRegisteredEvent = {
        electionId: electionId.toNumber(),
        voter,
        nationalId: nationalId.toString(),
        transactionHash: event.transactionHash
      };
      callback('VoterRegistered', eventData);
    });

    return () => {
      contract.removeAllListeners();
    };
  } catch (error) {
    console.error('Error setting up event listeners:', error);
    throw new Error('Failed to set up event listeners');
  }
};

export default {
  connectWallet,
  createElection,
  addCandidate,
  vote,
  selfRegisterVoter,
  registerVoter,
  pauseElection,
  resumeElection,
  isVoterRegistered,
  hasVoted,
  getElections,
  getElectionResults,
  changeAdmin,
  removeCandidate,
  updateElectionDetails,
  listenForEvents
};











