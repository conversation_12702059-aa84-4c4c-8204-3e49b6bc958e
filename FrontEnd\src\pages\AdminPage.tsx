import React, { useState } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { AdminVoterRegistration } from '../components/auth';
import { AlertCircle, CheckCircle, PlusCircle, Calendar, Clock, Database } from 'lucide-react';
import { ethers } from 'ethers';
import { FileUploader } from '../components/admin/FileUploader';
const CONTRACT_ADDRESS = import.meta.env.VITE_CONTRACT_ADDRESS;
import ElectionABI from '../../../artifacts/contracts/Election.sol/Election.json';

const AdminPage: React.FC = () => {
  const {
    state,
    createElection,
    addCandidate,
    changeAdmin,
    getElections
  } = useAppContext();

  const [activeTab, setActiveTab] = useState<'elections' | 'candidates' | 'voters' | 'settings'>('elections');

  // تحديث البيانات عند تغيير التبويب
  const handleTabChange = async (tab: 'elections' | 'candidates' | 'voters' | 'settings') => {
    setActiveTab(tab);
    // إعادة تحميل قائمة الانتخابات عند الانتقال إلى تبويب المرشحين
    if (tab === 'candidates') {
      try {
        await getElections();
      } catch (error) {
        console.error('فشل تحميل الانتخابات:', error);
      }
    }
  };
  const [selectedElectionId, setSelectedElectionId] = useState<number | null>(null);

  // إنشاء انتخابات جديدة
  const [newElectionTitle, setNewElectionTitle] = useState('');
  const [newElectionStartTime, setNewElectionStartTime] = useState('');
  const [newElectionEndTime, setNewElectionEndTime] = useState('');
  const [createElectionError, setCreateElectionError] = useState<string | null>(null);
  const [createElectionSuccess, setCreateElectionSuccess] = useState(false);
  const [createElectionLoading, setCreateElectionLoading] = useState(false);

  // إضافة مرشح جديد
  const [newCandidateName, setNewCandidateName] = useState('');
  const [newCandidateId, setNewCandidateId] = useState('');
  const [newCandidateDescription, setNewCandidateDescription] = useState('');
  const [newCandidateImageUrl, setNewCandidateImageUrl] = useState('');
  const [addCandidateError, setAddCandidateError] = useState<string | null>(null);
  const [addCandidateSuccess, setAddCandidateSuccess] = useState(false);
  const [addCandidateLoading, setAddCandidateLoading] = useState(false);

  // تغيير المسؤول
  const [newAdminAddress, setNewAdminAddress] = useState('');
  const [changeAdminError, setChangeAdminError] = useState<string | null>(null);
  const [changeAdminSuccess, setChangeAdminSuccess] = useState(false);
  const [changeAdminLoading, setChangeAdminLoading] = useState(false);

  // التحقق من البلوكتشين
  const [blockchainInfo, setBlockchainInfo] = useState<any>(null);
  const [blockchainInfoLoading, setBlockchainInfoLoading] = useState(false);

  // التحقق من صلاحيات المسؤول
  // useEffect redirect removed as AdminRoute handles this

  // التحقق من البيانات في البلوكتشين
  const verifyBlockchainData = async () => {
    try {
      setBlockchainInfoLoading(true);

      // الحصول على معلومات العقد الذكي
      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const contract = new ethers.Contract(CONTRACT_ADDRESS, ElectionABI.abi, provider);

      // الحصول على عدد الانتخابات
      const electionCount = await contract.electionCount();

      // الحصول على عنوان المسؤول
      const adminAddress = await contract.admin();

      // الحصول على معلومات الكتلة الحالية
      const blockNumber = await provider.getBlockNumber();
      const block = await provider.getBlock(blockNumber);

      // تخزين المعلومات
      setBlockchainInfo({
        electionCount: electionCount.toString(),
        adminAddress,
        blockNumber,
        blockHash: block.hash,
        blockTimestamp: new Date(block.timestamp * 1000).toLocaleString(),
        networkName: provider.network.name,
        chainId: provider.network.chainId
      });
    } catch (error) {
      console.error('خطأ في التحقق من البلوكتشين:', error);
    } finally {
      setBlockchainInfoLoading(false);
    }
  };

  const handleCreateElection = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setCreateElectionLoading(true);
      setCreateElectionError(null);

      // التحقق من المدخلات
      if (!newElectionTitle.trim()) {
        throw new Error('يرجى إدخال عنوان الانتخابات');
      }

      if (!newElectionStartTime || !newElectionEndTime) {
        throw new Error('يرجى تحديد وقت البدء والانتهاء');
      }

      // تحويل التواريخ إلى Timestamps
      const startTime = Math.floor(new Date(newElectionStartTime).getTime() / 1000);
      const endTime = Math.floor(new Date(newElectionEndTime).getTime() / 1000);
      const currentTime = Math.floor(Date.now() / 1000);

      // التحقق من صحة التواريخ
      if (startTime <= currentTime) {
        throw new Error('يجب أن يكون وقت البدء في المستقبل');
      }

      if (startTime >= endTime) {
        throw new Error('يجب أن يكون وقت البدء قبل وقت الانتهاء');
      }

      console.log('Creating election with:', { title: newElectionTitle, startTime, endTime });

      // إنشاء الانتخابات
      const newElectionId = await createElection(newElectionTitle, startTime, endTime);
      console.log('New election created with ID:', newElectionId);

      // تأخير قصير للتأكد من تحديث البلوكتشين
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إعادة تحميل قائمة الانتخابات
      await getElections();

      // اختيار الانتخابات الجديدة تلقائيًا
      setSelectedElectionId(newElectionId);

      // تنظيف النموذج
      setNewElectionTitle('');
      setNewElectionStartTime('');
      setNewElectionEndTime('');

      setCreateElectionSuccess(true);

      // التحقق من البلوكتشين للتأكد من إنشاء الانتخابات
      await verifyBlockchainData();

      // إخفاء رسالة النجاح بعد 3 ثوانٍ والانتقال إلى تبويب المرشحين
      setTimeout(() => {
        setCreateElectionSuccess(false);
        // الانتقال إلى تبويب المرشحين بعد إنشاء الانتخابات
        handleTabChange('candidates');
      }, 2000);

    } catch (err: any) {
      setCreateElectionError(err.message || 'فشل إنشاء الانتخابات');
      console.error('Create election error:', err);
    } finally {
      setCreateElectionLoading(false);
    }
  };

  const handleAddCandidate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedElectionId === null) {
      setAddCandidateError('يرجى اختيار انتخابات');
      return;
    }

    if (!newCandidateName) {
      setAddCandidateError('يرجى إدخال اسم المرشح');
      return;
    }

    // تم إزالة التحقق من معرف المرشح لأنه يتم تعيينه تلقائيًا

    if (!newCandidateImageUrl) {
      setAddCandidateError('يرجى رفع صورة للمرشح');
      return;
    }

    // التحقق من حجم رابط الصورة
    if (newCandidateImageUrl.length > 1000000) { // أكثر من 1 ميجابايت
      console.warn('Image URL is too large:', Math.round(newCandidateImageUrl.length / 1024), 'KB');
      setAddCandidateError('حجم الصورة كبير جداً. يرجى استخدام صورة أصغر.');
      return;
    }

    try {
      setAddCandidateLoading(true);
      setAddCandidateError(null);

      // طباعة معلومات مفصلة للتشخيص
      const imageUrlLength = newCandidateImageUrl ? newCandidateImageUrl.length : 0;
      const imageUrlSizeKB = newCandidateImageUrl ? Math.round(newCandidateImageUrl.length / 1024) : 0;

      console.log('Adding candidate:', {
        electionId: selectedElectionId,
        candidateId: parseInt(newCandidateId),
        name: newCandidateName,
        description: newCandidateDescription,
        imageUrlLength,
        imageUrlSizeKB
      });

      // التحقق من حجم الصورة مرة أخرى
      if (imageUrlSizeKB > 200) {
        setAddCandidateError('حجم الصورة كبير جداً. يجب أن يكون أقل من 200 كيلوبايت.');
        return;
      }

      // طباعة معلومات إضافية للتشخيص
      console.log(`Image URL starts with: ${newCandidateImageUrl?.substring(0, 30)}...`);
      console.log(`Image URL format: ${newCandidateImageUrl?.startsWith('data:image/webp') ? 'webp' :
                                     newCandidateImageUrl?.startsWith('data:image/jpeg') ? 'jpeg' :
                                     newCandidateImageUrl?.startsWith('data:image/png') ? 'png' : 'unknown'}`);
      console.log(`Candidate ID type: ${typeof newCandidateId}, parsed value: ${parseInt(newCandidateId)}`);

      // استخدام عدد المرشحين الحاليين كمعرف للمرشح الجديد
      const candidateId = state.elections.find(e => e.id === selectedElectionId)?.candidates.length || 0;
      console.log(`Calling addCandidate function with candidateId=${candidateId}...`);
      await addCandidate(
        selectedElectionId,
        candidateId,
        newCandidateName,
        newCandidateDescription,
        newCandidateImageUrl
      );
      console.log('addCandidate function completed successfully');

      // تأخير قصير للتأكد من تحديث البلوكتشين
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إعادة تحميل قائمة الانتخابات للتأكد من تحديث البيانات
      await getElections();

      // التحقق من البلوكتشين للتأكد من إضافة المرشح
      await verifyBlockchainData();

      setAddCandidateSuccess(true);
      setNewCandidateName('');
      setNewCandidateId('');
      setNewCandidateDescription('');
      setNewCandidateImageUrl('');

      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setAddCandidateSuccess(false);
      }, 3000);
    } catch (err: any) {
      console.error('Add candidate error:', err);

      // تحليل الخطأ بشكل أفضل
      let errorMessage = err.message || 'فشل إضافة المرشح';

      // التحقق من أنواع الأخطاء المحتملة
      if (err.code === 'ACTION_REJECTED' || errorMessage.includes('تم رفض المعاملة')) {
        errorMessage = 'تم رفض المعاملة من قبل المستخدم';
      } else if (errorMessage.includes('حجم البيانات كبير') ||
                 errorMessage.includes('gas') ||
                 errorMessage.includes('exceeds')) {
        errorMessage = 'حجم الصورة كبير جداً للمعاملة. يرجى استخدام صورة أصغر.';
      } else if (errorMessage.includes('معرف المرشح موجود')) {
        errorMessage = 'معرف المرشح موجود بالفعل. يرجى استخدام معرف آخر.';
      }

      // طباعة الخطأ المفصل
      console.error('Detailed error:', {
        message: err.message,
        code: err.code,
        data: err.data,
        stack: err.stack
      });

      setAddCandidateError(errorMessage);
    } finally {
      setAddCandidateLoading(false);
    }
  };

  const handleChangeAdmin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newAdminAddress || !ethers.utils.isAddress(newAdminAddress)) {
      setChangeAdminError('يرجى إدخال عنوان محفظة صالح');
      return;
    }

    try {
      setChangeAdminLoading(true);
      setChangeAdminError(null);
      await changeAdmin(newAdminAddress);
      setChangeAdminSuccess(true);
      setNewAdminAddress('');

      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setChangeAdminSuccess(false);
      }, 3000);
    } catch (err: any) {
      setChangeAdminError(err.message || 'فشل تغيير المسؤول');
    } finally {
      setChangeAdminLoading(false);
    }
  };

  if (!state.user?.isAdmin) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p className="font-medium">غير مصرح</p>
          <p className="text-sm">يجب أن تكون مسؤولاً للوصول إلى هذه الصفحة</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">لوحة تحكم المسؤول</h1>

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="flex border-b">
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'elections'
                ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('elections')}
          >
            إدارة الانتخابات
          </button>
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'candidates'
                ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('candidates')}
          >
            إدارة المرشحين
          </button>
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'voters'
                ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('voters')}
          >
            إدارة الناخبين
          </button>
          <button
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'settings'
                ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('settings')}
          >
            الإعدادات
          </button>
        </div>

        <div className="p-6">
          {/* إدارة الانتخابات */}
          {activeTab === 'elections' && (
            <div>
              <h2 className="text-xl font-bold mb-4">إنشاء انتخابات جديدة</h2>

              {createElectionError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  <span>{createElectionError}</span>
                </div>
              )}

              {createElectionSuccess && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  <span>تم إنشاء الانتخابات بنجاح</span>
                </div>
              )}

              <form onSubmit={handleCreateElection} className="space-y-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="electionTitle">
                    عنوان الانتخابات
                  </label>
                  <input
                    id="electionTitle"
                    type="text"
                    value={newElectionTitle}
                    onChange={(e) => setNewElectionTitle(e.target.value)}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="أدخل عنوان الانتخابات"
                    disabled={createElectionLoading}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="startTime">
                      وقت البدء
                    </label>
                    <input
                      id="startTime"
                      type="datetime-local"
                      value={newElectionStartTime}
                      onChange={(e) => setNewElectionStartTime(e.target.value)}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      disabled={createElectionLoading}
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="endTime">
                      وقت الانتهاء
                    </label>
                    <input
                      id="endTime"
                      type="datetime-local"
                      value={newElectionEndTime}
                      onChange={(e) => setNewElectionEndTime(e.target.value)}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      disabled={createElectionLoading}
                    />
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={createElectionLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  <PlusCircle className="w-5 h-5 ml-2" />
                  {createElectionLoading ? 'جاري الإنشاء...' : 'إنشاء انتخابات'}
                </button>
              </form>

              <hr className="my-8" />

              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">الانتخابات الحالية</h2>
                <button
                  onClick={verifyBlockchainData}
                  disabled={blockchainInfoLoading}
                  className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  <Database className="w-5 h-5 ml-2" />
                  {blockchainInfoLoading ? 'جاري التحقق...' : 'التحقق من البلوكتشين'}
                </button>
              </div>

              {blockchainInfo && (
                <div className="bg-purple-50 border border-purple-200 text-purple-700 px-4 py-3 rounded mb-4">
                  <h3 className="font-bold mb-2">معلومات البلوكتشين</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div><span className="font-semibold">عدد الانتخابات:</span> {blockchainInfo.electionCount}</div>
                    <div><span className="font-semibold">عنوان المسؤول:</span> {blockchainInfo.adminAddress}</div>
                    <div><span className="font-semibold">رقم الكتلة:</span> {blockchainInfo.blockNumber}</div>
                    <div><span className="font-semibold">الشبكة:</span> {blockchainInfo.networkName} (Chain ID: {blockchainInfo.chainId})</div>
                    <div><span className="font-semibold">وقت الكتلة:</span> {blockchainInfo.blockTimestamp}</div>
                    <div className="md:col-span-2"><span className="font-semibold">هاش الكتلة:</span> <span className="text-xs">{blockchainInfo.blockHash}</span></div>
                  </div>
                </div>
              )}

              {state.elections.length === 0 ? (
                <p className="text-center text-gray-600">لا توجد انتخابات متاحة</p>
              ) : (
                <div className="space-y-4">
                  {state.elections.map((election) => {
                    const now = Math.floor(Date.now() / 1000);
                    const isActive = now >= election.startTime && now <= election.endTime && !election.isPaused;
                    const hasStarted = now >= election.startTime;
                    const hasEnded = now > election.endTime;

                    const formatDate = (timestamp: number) => {
                      return new Date(timestamp * 1000).toLocaleString('ar-SA');
                    };

                    return (
                      <div key={election.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex justify-between items-start">
                          <h3 className="font-bold text-lg">{election.title}</h3>

                          <div>
                            {isActive ? (
                              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                <Clock className="w-4 h-4 ml-1" />
                                نشط حال
                              </span>
                            ) : election.isPaused ? (
                              <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                <Clock className="w-4 h-4 ml-1" />
                                متوقف مؤقت
                              </span>
                            ) : hasEnded ? (
                              <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                <Clock className="w-4 h-4 ml-1" />
                                انتهى
                              </span>
                            ) : !hasStarted ? (
                              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                <Clock className="w-4 h-4 ml-1" />
                                لم يبدأ بعد
                              </span>
                            ) : null}
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-4 mb-4 text-gray-600">
                          <div className="flex items-center">
                            <Calendar className="w-5 h-5 ml-2" />
                            <div>
                              <p className="text-sm">بداية: {formatDate(election.startTime)}</p>
                              <p className="text-sm">نهاية: {formatDate(election.endTime)}</p>
                            </div>
                          </div>

                          <div className="flex items-center">
                            <span className="text-sm">عدد المرشحين: {election.candidateCount}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {/* إدارة المرشحين */}
          {activeTab === 'candidates' && (
            <div>
              <h2 className="text-xl font-bold mb-4">إضافة مرشح جديد</h2>

              {addCandidateError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  <span>{addCandidateError}</span>
                </div>
              )}

              {addCandidateSuccess && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  <span>تم إضافة المرشح بنجاح</span>
                </div>
              )}

              <form onSubmit={handleAddCandidate} className="space-y-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="electionSelect">
                    اختر الانتخابات
                  </label>
                  <select
                    id="electionSelect"
                    value={selectedElectionId !== null ? selectedElectionId.toString() : ''}
                    onChange={(e) => setSelectedElectionId(parseInt(e.target.value))}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    disabled={addCandidateLoading}
                  >
                    <option value="">-- اختر الانتخابات --</option>
                    {state.elections.map((election) => (
                      <option key={election.id} value={election.id}>
                        {election.title}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="candidateId">
                      معرف المرشح
                    </label>
                    <div className="relative">
                      <input
                        id="candidateId"
                        type="number"
                        value={selectedElectionId !== null ? state.elections.find(e => e.id === selectedElectionId)?.candidates.length || 0 : ''}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100"
                        placeholder="معرف المرشح التلقائي"
                        disabled={true}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-sm text-gray-500">(تلقائي)</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">يتم تعيين معرف المرشح تلقائيًا بناءً على عدد المرشحين الحاليين</p>
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="candidateName">
                      اسم المرشح
                    </label>
                    <input
                      id="candidateName"
                      type="text"
                      value={newCandidateName}
                      onChange={(e) => setNewCandidateName(e.target.value)}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      placeholder="أدخل اسم المرشح"
                      disabled={addCandidateLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="candidateDescription">
                    وصف المرشح
                  </label>
                  <textarea
                    id="candidateDescription"
                    value={newCandidateDescription}
                    onChange={(e) => setNewCandidateDescription(e.target.value)}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="أدخل وصف المرشح"
                    rows={3}
                    disabled={addCandidateLoading}
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">
                    صورة المرشح
                  </label>
                  <FileUploader
                    onImageSelect={(url) => {
                      console.log('Image URL received in AdminPage:', url);
                      setNewCandidateImageUrl(url);
                    }}
                  />
                  {newCandidateImageUrl && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 break-all">
                        <span className="font-semibold">رابط الصورة:</span> {newCandidateImageUrl.substring(0, 20)}...{newCandidateImageUrl.substring(newCandidateImageUrl.length - 10)}
                      </p>
                    </div>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={addCandidateLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  <PlusCircle className="w-5 h-5 ml-2" />
                  {addCandidateLoading ? 'جاري الإضافة...' : 'إضافة مرشح'}
                </button>
              </form>
            </div>
          )}

          {/* إدارة الناخبين */}
          {activeTab === 'voters' && (
            <div>
              <h2 className="text-xl font-bold mb-4">تسجيل ناخب جديد</h2>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="voterElectionSelect">
                  اختر الانتخابات
                </label>
                <select
                  id="voterElectionSelect"
                  value={selectedElectionId !== null ? selectedElectionId.toString() : ''}
                  onChange={(e) => setSelectedElectionId(parseInt(e.target.value))}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                >
                  <option value="">-- اختر الانتخابات --</option>
                  {state.elections.map((election) => (
                    <option key={election.id} value={election.id}>
                      {election.title}
                    </option>
                  ))}
                </select>
              </div>

              {selectedElectionId !== null ? (
                <AdminVoterRegistration electionId={selectedElectionId} />
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
                  <p className="font-medium">يرجى اختيار انتخابات</p>
                  <p className="text-sm">اختر انتخابات لتسجيل ناخب جديد</p>
                </div>
              )}
            </div>
          )}

          {/* الإعدادات */}
          {activeTab === 'settings' && (
            <div>
              <h2 className="text-xl font-bold mb-4">تغيير المسؤول</h2>

              {changeAdminError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  <span>{changeAdminError}</span>
                </div>
              )}

              {changeAdminSuccess && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  <span>تم تغيير المسؤول بنجاح</span>
                </div>
              )}

              <form onSubmit={handleChangeAdmin} className="space-y-4">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="newAdminAddress">
                    عنوان المسؤول الجديد
                  </label>
                  <input
                    id="newAdminAddress"
                    type="text"
                    value={newAdminAddress}
                    onChange={(e) => setNewAdminAddress(e.target.value)}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="0x..."
                    disabled={changeAdminLoading}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    تحذير: بعد تغيير المسؤول، لن تتمكن من الوصول إلى لوحة التحكم هذه بعد الآن
                  </p>
                </div>

                <button
                  type="submit"
                  disabled={changeAdminLoading}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {changeAdminLoading ? 'جاري التغيير...' : 'تغيير المسؤول'}
                </button>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPage;










