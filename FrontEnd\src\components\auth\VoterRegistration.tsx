import React, { useState, useEffect } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { AlertCircle, CheckCircle, UserPlus } from 'lucide-react';

interface VoterRegistrationProps {
  electionId: number;
  onSuccess?: () => void;
}

export const VoterRegistration: React.FC<VoterRegistrationProps> = ({
  electionId,
  onSuccess
}) => {
  const { selfRegisterVoter, isVoterRegistered, state } = useAppContext();
  const [nationalId, setNationalId] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);

  // التحقق من تسجيل المستخدم الحالي
  useEffect(() => {
    let isMounted = true;

    const checkRegistration = async () => {
      if (!state.user || !isMounted) return;

      try {
        console.log(`VoterRegistration: Checking if user ${state.user.address} is registered for election ${electionId}`);
        const registered = await isVoterRegistered(electionId, state.user.address);

        if (isMounted) {
          console.log(`VoterRegistration: User registration status: ${registered}`);
          setIsRegistered(registered);
        }
      } catch (err) {
        console.error('خطأ في التحقق من تسجيل الناخب:', err);
      }
    };

    checkRegistration();

    return () => {
      isMounted = false;
    };
  }, [electionId, state.user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!state.user) {
      setError('يجب الاتصال بالمحفظة أولاً');
      return;
    }

    if (!nationalId || nationalId.length !== 11 || !/^\d+$/.test(nationalId)) {
      setError('الرقم الوطني يجب أن يتكون من 11 رقم');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('Registering voter with national ID:', nationalId, 'for election:', electionId);

      // تحويل الرقم الوطني إلى رقم صحيح
      // استخدام BigInt لتجنب مشاكل الأرقام الكبيرة
      const nationalIdNumber = Number(nationalId);

      console.log('Converted national ID to number:', nationalIdNumber);

      // التحقق من أن الرقم الوطني صالح
      if (isNaN(nationalIdNumber)) {
        throw new Error('الرقم الوطني غير صالح');
      }

      // محاولة التسجيل مع معالجة الأخطاء
      try {
        await selfRegisterVoter(electionId, nationalIdNumber);
        console.log('Self-registration transaction sent successfully');

        // تأخير قصير للتأكد من تحديث البلوكتشين
        await new Promise(resolve => setTimeout(resolve, 2000));

        // التحقق مرة أخرى من حالة التسجيل
        try {
          const registered = await isVoterRegistered(electionId, state.user.address);
          console.log('Registration status after submission:', registered);

          setSuccess(true);
          setIsRegistered(registered);

          if (onSuccess) onSuccess();
        } catch (verifyErr) {
          console.error('Error verifying registration status:', verifyErr);
          // افترض أن التسجيل نجح حتى لو فشل التحقق
          setSuccess(true);
          setIsRegistered(true);
          if (onSuccess) onSuccess();
        }
      } catch (regErr: any) {
        console.error('Error during self-registration:', regErr);
        throw regErr; // إعادة رمي الخطأ للمعالجة العامة
      }
    } catch (err: any) {
      console.error('Voter registration error:', err);
      setError(err.message || 'فشل تسجيل الناخب');
    } finally {
      setLoading(false);
    }
  };

  if (isRegistered) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center justify-center mb-4">
          <CheckCircle className="w-12 h-12 text-green-600" />
        </div>

        <h2 className="text-2xl font-bold text-center mb-4">تم تسجيلك بنجاح</h2>

        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
          <p>أنت مسجل بالفعل كناخب في هذه الانتخابات</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-4">
        <UserPlus className="w-12 h-12 text-blue-600" />
      </div>

      <h2 className="text-2xl font-bold text-center mb-4">تسجيل ناخب جديد</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <CheckCircle className="w-5 h-5 mr-2" />
          <span>تم تسجيلك بنجاح كناخب في هذه الانتخابات</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="nationalId">
            الرقم الوطني
          </label>
          <input
            id="nationalId"
            type="text"
            value={nationalId}
            onChange={(e) => setNationalId(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="أدخل الرقم الوطني المكون من 11 رقم"
            maxLength={11}
            disabled={loading || !state.user}
          />
          <p className="text-xs text-gray-500 mt-1">
            الرقم الوطني يجب أن يتكون من 11 رقم
          </p>
        </div>

        <button
          type="submit"
          disabled={loading || !state.user}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'جاري التسجيل...' : 'تسجيل'}
        </button>
      </form>

      {!state.user && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p className="font-medium">يجب الاتصال بالمحفظة أولاً</p>
          <p className="text-sm">قم بالاتصال بمحفظتك للتمكن من التسجيل كناخب</p>
        </div>
      )}
    </div>
  );
};
