## ربط الواجهة الأمامية بالعقد الذكي
- [x] إنشاء خدمة للتفاعل مع العقد الذكي (contractService.ts)
- [x] تحديث سياق التطبيق (AppContext) لاستخدام خدمة العقد
- [x] التعامل مع الأخطاء والاستثناءات
- [x] تنفيذ آلية التحديث التلقائي للبيانات

## اختبار التطبيق
- [ ] اختبار تسجيل الناخبين
- [ ] اختبار إنشاء الانتخابات وإدارتها
- [ ] اختبار إضافة المرشحين ورفع صورهم
- [ ] اختبار عملية التصويت
- [ ] اختبار عرض النتائج
