// SPDX-License-Identifier: MIT
pragma solidity ^0.8.21;

// واجهة للوظائف الأساسية للانتخابات
interface IBaseElection {
    function createElection(string memory title, uint startTime, uint endTime) external;
    function pauseElection(uint electionId) external;
    function resumeElection(uint electionId) external;
}

// واجهة للوظائف المتعلقة بإدارة الناخبين
interface IVoterManagement {
    function registerVoter(uint electionId, address voter, uint nationalId) external;
    function selfRegister(uint electionId, uint nationalId) external;
}

// واجهة للوظائف المتعلقة بإدارة المرشحين
interface ICandidateManagement {
    function addCandidate(uint electionId, uint candidateId, string memory name, string memory description, string memory imageURL) external;
    function removeCandidate(uint electionId, uint candidateId) external;
}

// عقد الانتخابات 
contract Election is IBaseElection, IVoterManagement, ICandidateManagement {
    // هيكل بيانات للمرشح
    struct Candidate {
        string name;          // اسم المرشح
        string description;   // وصف المرشح
        string imageURL;      // رابط صورة المرشح
        uint voteCount;       // عدد الأصوات التي حصل عليها المرشح
    }

    // هيكل بيانات لإدارة الانتخابات
    struct ElectionDetails {
        string title;          // عنوان الانتخابات
        uint startTime;        // توقيت بدء الانتخابات
        uint endTime;          // توقيت انتهاء الانتخابات
        bool isPaused;         // حالة إيقاف الانتخابات
        mapping(address => bool) hasVoted; // خريطة لتتبع من صوت
        mapping(uint => Candidate) candidates; // خريطة لتخزين المرشحين باستخدام معرف فريد
        uint candidateCount;   // عدد المرشحين
        mapping(address => bool) registeredVoters; // خريطة لتسجيل الناخبين
        mapping(uint => bool) nationalIdUsed; // خريطة لتتبع الأرقام الوطنية المستخدمة
    }

    mapping(uint => ElectionDetails) public elections; // خريطة لتخزين الانتخابات بالاعتماد على معرفها
    uint public electionCount; // عداد الانتخابات
    address public admin; // عنوان المسؤول الذي يدير الانتخابات

    event ElectionCreated(uint electionId, string title); // حدث يتم إطلاقه عند إنشاء انتخابات
    event VoteCast(uint electionId, uint candidateId, address voter); // حدث يتم إطلاقه عند التصويت
    event CandidateAdded(uint electionId, uint candidateId, string name); // حدث يتم إطلاقه عند إضافة مرشح
    event ElectionPaused(uint electionId); // حدث يتم إطلاقه عند إيقاف الانتخابات
    event ElectionResumed(uint electionId); // حدث يتم إطلاقه عند استئناف الانتخابات
    event AdminChanged(address oldAdmin, address newAdmin); // حدث يتم إطلاقه عند تغيير المسؤول
    event VoterRegistered(uint electionId, address voter, uint nationalId); // حدث يتم إطلاقه عند تسجيل ناخب

    // تعديل للوصول إلى الوظائف فقط من قبل المسؤول
    modifier onlyAdmin() {
        require(msg.sender == admin, "Only admin can perform this action.");
        _;
    }

    // تعديل للوصول إلى الوظائف فقط أثناء فترة الانتخابات
    modifier onlyDuringElection(uint electionId) {
        require(electionId < electionCount, "Election does not exist.");
        require(block.timestamp >= elections[electionId].startTime && block.timestamp <= elections[electionId].endTime, "Election is not active.");
        require(!elections[electionId].isPaused, "Election is paused.");
        _;
    }

    // تعديل للوصول إلى الوظائف فقط قبل بدء الانتخابات
    modifier onlyBeforeElection(uint electionId) {
        require(electionId < electionCount, "Election does not exist.");
        require(block.timestamp < elections[electionId].startTime, "Election has already started.");
        _;
    }

    // تعيين المسؤول عند نشر العقد
    constructor() {
        admin = msg.sender;
    }

    // وظيفة لتغيير المسؤول
    function changeAdmin(address newAdmin) public onlyAdmin {
        require(newAdmin != address(0), "Invalid address.");
        emit AdminChanged(admin, newAdmin);
        admin = newAdmin;
    }

    // وظيفة لإنشاء انتخابات جديدة
    function createElection(string memory title, uint startTime, uint endTime) public override onlyAdmin {
        require(startTime < endTime, "Invalid time range.");
        ElectionDetails storage newElection = elections[electionCount++];
        newElection.title = title;
        newElection.startTime = startTime;
        newElection.endTime = endTime;
        newElection.isPaused = false;

        emit ElectionCreated(electionCount - 1, title);
    }

    // وظيفة لتسجيل الناخبين (من قبل المسؤول)
    function registerVoter(uint electionId, address voter, uint nationalId) public override onlyAdmin onlyBeforeElection(electionId) {
        require(electionId < electionCount, "Election does not exist.");
        require(!elections[electionId].registeredVoters[voter], "Voter is already registered.");
        require(!elections[electionId].nationalIdUsed[nationalId], "National ID is already used.");
        elections[electionId].registeredVoters[voter] = true;
        elections[electionId].nationalIdUsed[nationalId] = true;

        emit VoterRegistered(electionId, voter, nationalId);
    }

    // وظيفة للتسجيل الذاتي للناخبين
    function selfRegister(uint electionId, uint nationalId) public override onlyBeforeElection(electionId) {
        require(electionId < electionCount, "Election does not exist.");
        require(!elections[electionId].registeredVoters[msg.sender], "You are already registered.");
        require(!elections[electionId].nationalIdUsed[nationalId], "National ID is already used.");
        elections[electionId].registeredVoters[msg.sender] = true;
        elections[electionId].nationalIdUsed[nationalId] = true;

        emit VoterRegistered(electionId, msg.sender, nationalId);
    }

    // وظيفة لإضافة مرشح جديد إلى انتخابات معينة
    function addCandidate(uint electionId, uint candidateId, string memory name, string memory description, string memory imageURL) public override onlyAdmin onlyBeforeElection(electionId) {
        require(elections[electionId].candidates[candidateId].voteCount == 0, "Candidate ID already exists.");
        elections[electionId].candidates[candidateId] = Candidate(name, description, imageURL, 0);
        elections[electionId].candidateCount++;

        emit CandidateAdded(electionId, candidateId, name);
    }

    // وظيفة للتصويت لمرشح معين في انتخابات محددة
    function vote(uint electionId, uint candidateId) public onlyDuringElection(electionId) {
        ElectionDetails storage election = elections[electionId];
        require(election.registeredVoters[msg.sender], "You are not registered to vote.");
        require(!election.hasVoted[msg.sender], "You've already voted.");

        // التحقق من وجود المرشح باستخدام معرفه
        require(bytes(election.candidates[candidateId].name).length > 0, "Candidate does not exist.");

        election.candidates[candidateId].voteCount++;
        election.hasVoted[msg.sender] = true;

        emit VoteCast(electionId, candidateId, msg.sender);
    }

    // وظيفة لإيقاف انتخابات معينة
    function pauseElection(uint electionId) public override onlyAdmin {
        require(electionId < electionCount, "Election does not exist.");
        elections[electionId].isPaused = true;

        emit ElectionPaused(electionId);
    }

    // وظيفة لاستئناف انتخابات معينة
    function resumeElection(uint electionId) public override onlyAdmin {
        require(electionId < electionCount, "Election does not exist.");
        elections[electionId].isPaused = false;

        emit ElectionResumed(electionId);
    }

    // وظيفة للحصول على نتائج انتخابات معينة
    function getElectionResults(uint electionId) public view returns (Candidate[] memory) {
        require(electionId < electionCount, "Election does not exist.");
        ElectionDetails storage election = elections[electionId];
        Candidate[] memory results = new Candidate[](election.candidateCount);

        // استخدام نفس معرف المرشح للاسترجاع
        uint resultIndex = 0;
        for (uint i = 0; i < 100; i++) { // التحقق من المعرفات من 0 إلى 99
            if (election.candidates[i].voteCount > 0 || bytes(election.candidates[i].name).length > 0) {
                results[resultIndex] = election.candidates[i];
                resultIndex++;
                if (resultIndex >= election.candidateCount) {
                    break;
                }
            }
        }
        return results;
    }

    // وظيفة لإزالة مرشح من الانتخابات قبل بدء الانتخابات
    function removeCandidate(uint electionId, uint candidateId) public override onlyAdmin onlyBeforeElection(electionId) {
        require(electionId < electionCount, "Election does not exist.");
        // التحقق من وجود المرشح باستخدام معرفه
        require(bytes(elections[electionId].candidates[candidateId].name).length > 0, "Candidate does not exist.");
        delete elections[electionId].candidates[candidateId];
        elections[electionId].candidateCount--;
    }

    // وظيفة لتعديل تفاصيل الانتخابات قبل بدء الانتخابات
    function updateElectionDetails(uint electionId, string memory title, uint startTime, uint endTime) public onlyAdmin onlyBeforeElection(electionId) {
        require(electionId < electionCount, "Election does not exist.");
        require(startTime < endTime, "Invalid time range.");
        ElectionDetails storage election = elections[electionId];
        election.title = title;
        election.startTime = startTime;
        election.endTime = endTime;
    }

    // وظيفة للتحقق من تسجيل الناخب
    function isVoterRegistered(uint electionId, address voter) public view returns (bool) {
        require(electionId < electionCount, "Election does not exist.");
        return elections[electionId].registeredVoters[voter];
    }

    // وظيفة للتحقق من تصويت الناخب
    function hasVoted(uint electionId, address voter) public view returns (bool) {
        require(electionId < electionCount, "Election does not exist.");
        return elections[electionId].hasVoted[voter];
    }
}