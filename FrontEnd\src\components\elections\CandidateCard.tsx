import React from 'react';
import { Candidate } from '../../interfaces';
import { Vote } from 'lucide-react';
import { getDisplayableImageUrl, isValidImageUrl } from '../../utils/imageHelper';

interface CandidateCardProps {
  candidate: Candidate;
  onVote: () => void;
  canVote: boolean;
  loading: boolean;
  showVotes?: boolean;
}

export const CandidateCard: React.FC<CandidateCardProps> = ({
  candidate,
  onVote,
  canVote,
  loading,
  showVotes = true
}) => {
  // طباعة معلومات المرشح للتشخيص
  console.log('CandidateCard rendering for:', candidate.name);
  console.log('Candidate data:', candidate);
  console.log('Image URL valid:', isValidImageUrl(candidate.imageUrl));
  console.log('Displayable URL:', getDisplayableImageUrl(candidate.imageUrl));
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="h-48 overflow-hidden bg-gray-100">
        {isValidImageUrl(candidate.imageUrl) ? (
          <img
            src={getDisplayableImageUrl(candidate.imageUrl)}
            alt={candidate.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              console.log('Image load error for:', candidate.name);
              console.log('Image URL:', candidate.imageUrl);
              console.log('Image URL starts with:', candidate.imageUrl?.substring(0, 30));

              // محاولة إصلاح الرابط إذا كان مقطوعاً
              if (candidate.imageUrl && candidate.imageUrl.includes('data:image/')) {
                const dataUrlIndex = candidate.imageUrl.indexOf('data:image/');
                if (dataUrlIndex >= 0) {
                  const fixedUrl = candidate.imageUrl.substring(dataUrlIndex);
                  console.log('Attempting to fix URL:', fixedUrl.substring(0, 30));
                  e.currentTarget.src = fixedUrl;
                  return;
                }
              }

              e.currentTarget.src = 'https://via.placeholder.com/300x200?text=صورة+المرشح';
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-200">
            <span className="text-gray-500 text-lg">صورة المرشح غير متوفرة</span>
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="text-xl font-bold mb-2">{candidate.name}</h3>

        {candidate.description && (
          <p className="text-gray-600 mb-4">{candidate.description}</p>
        )}

        {showVotes && (
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-700 font-medium">عدد الأصوات:</span>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              {candidate.voteCount}
            </span>
          </div>
        )}

        {canVote && (
          <button
            onClick={onVote}
            disabled={loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            <Vote className="w-5 h-5 ml-2" />
            {loading ? 'جاري التصويت...' : 'تصويت'}
          </button>
        )}
      </div>
    </div>
  );
};
