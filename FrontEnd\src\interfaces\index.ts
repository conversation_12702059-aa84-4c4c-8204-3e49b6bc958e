// أنواع البيانات الأساسية للتطبيق

// نوع بيانات المرشح
export interface Candidate {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  voteCount: number;
}

// نوع بيانات الانتخابات
export interface Election {
  id: number;
  title: string;
  startTime: number;
  endTime: number;
  isPaused: boolean;
  candidates: Candidate[];
  candidateCount: number;
}

// نوع بيانات المستخدم
export interface User {
  address: string;
  isAdmin: boolean;
  nationalId?: number;
  hasVoted?: boolean;
  isRegistered?: boolean;
}

// نوع بيانات حالة التطبيق
export interface AppState {
  user: User | null;
  elections: Election[];
  currentElection: Election | null;
  loading: boolean;
  error: string | null;
}

// أنواع بيانات الأحداث
export interface ElectionEvent {
  transactionHash: string;
}

export interface ElectionCreatedEvent extends ElectionEvent {
  electionId: number;
  title: string;
  startTime: number;
  endTime: number;
}

export interface VoteCastEvent extends ElectionEvent {
  electionId: number;
  candidateId: number;
  voter: string;
}

export interface CandidateAddedEvent extends ElectionEvent {
  electionId: number;
  candidateId: number;
  name: string;
}

export interface ElectionPausedEvent extends ElectionEvent {
  electionId: number;
}

export interface ElectionResumedEvent extends ElectionEvent {
  electionId: number;
}

export interface AdminChangedEvent extends ElectionEvent {
  oldAdmin: string;
  newAdmin: string;
}

export interface VoterRegisteredEvent extends ElectionEvent {
  electionId: number;
  voter: string;
  nationalId: string;
}

// نوع بيانات معالج الأحداث
export type EventCallback = (
  eventName: string,
  data:
    | ElectionCreatedEvent
    | VoteCastEvent
    | CandidateAddedEvent
    | ElectionPausedEvent
    | ElectionResumedEvent
    | AdminChangedEvent
    | VoterRegisteredEvent
) => void;

// نوع بيانات سياق التطبيق
export interface AppContextType {
  state: AppState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  registerVoter: (electionId: number, nationalId: number) => Promise<void>;
  vote: (electionId: number, candidateId: number) => Promise<void>;
  getElections: () => Promise<void>;
  getElectionResults: (electionId: number) => Promise<Candidate[]>;
  createElection: (title: string, startTime: number, endTime: number) => Promise<void>;
  addCandidate: (electionId: number, candidateId: number, name: string, description: string, imageURL: string) => Promise<void>;
  pauseElection: (electionId: number) => Promise<void>;
  resumeElection: (electionId: number) => Promise<void>;
  listenForEvents: (callback: EventCallback) => () => void;
}

