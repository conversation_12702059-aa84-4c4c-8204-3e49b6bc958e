/* استيراد أنماط Slick Carousel */
@import 'slick-carousel/slick/slick.css';
@import 'slick-carousel/slick/slick-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
  }

  body {
    font-family: system-ui, -apple-system, sans-serif;
  }
}

/* أنماط Slick Carousel المخصصة */
.slick-prev:before,
.slick-next:before {
  color: #4F46E5;
  font-size: 24px;
}

.slick-dots li button:before {
  color: #4F46E5;
}

.slick-dots li.slick-active button:before {
  color: #4F46E5;
}

.feature-card {
  transition: all 0.3s ease;
  height: 100%;
  margin: 10px;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* تعديلات للتوافق مع اللغة العربية */
.slick-slider {
  direction: ltr;
}

.slick-slide {
  float: left;
  text-align: right;
}

/* تعديلات للأجهزة المحمولة */
@media (max-width: 640px) {
  .slick-prev, .slick-next {
    z-index: 1;
  }

  .slick-prev {
    left: 5px;
  }

  .slick-next {
    right: 5px;
  }
}