import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import * as contractService from '../utils/contractService';
import { Election, User, Candidate } from '../interfaces';

// تعريف نوع حالة السياق
interface AppState {
  user: User | null;
  elections: Election[];
  loading: boolean;
  error: string | null;
}

// تعريف نوع سياق التطبيق
interface AppContextType {
  state: AppState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  getElections: () => Promise<void>;
  getElectionResults: (electionId: number) => Promise<Candidate[]>;
  createElection: (title: string, startTime: number, endTime: number) => Promise<number>;
  updateElectionDetails: (electionId: number, title: string, startTime: number, endTime: number) => Promise<void>;
  addCandidate: (electionId: number, candidateId: number, name: string, description: string, imageUrl: string) => Promise<void>;
  removeCandidate: (electionId: number, candidateId: number) => Promise<void>;
  vote: (electionId: number, candidateId: number) => Promise<void>;
  selfRegisterVoter: (electionId: number, nationalId: number) => Promise<void>;
  registerVoter: (electionId: number, voterAddress: string, nationalId: number) => Promise<void>;
  isVoterRegistered: (electionId: number, voterAddress: string) => Promise<boolean>;
  hasVoted: (electionId: number, voterAddress: string) => Promise<boolean>;
  pauseElection: (electionId: number) => Promise<void>;
  resumeElection: (electionId: number) => Promise<void>;
  changeAdmin: (newAdminAddress: string) => Promise<void>;
  listenForEvents: (callback: (eventName: string, data: any) => void) => () => void;
}

// إنشاء سياق التطبيق
const AppContext = createContext<AppContextType | undefined>(undefined);

// مزود سياق التطبيق
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // حالة التطبيق
  const [state, setState] = useState<AppState>({
    user: null,
    elections: [],
    loading: false,
    error: null
  });

  // الاتصال بالمحفظة
  const connectWallet = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const user = await contractService.connectWallet();
      console.log('Connected User:', user); // Add this debug log
      setState(prev => ({
        ...prev,
        user,
        loading: false
      }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // قطع الاتصال بالمحفظة
  const disconnectWallet = () => {
    setState(prev => ({ ...prev, user: null }));
  };

  // الحصول على قائمة الانتخابات
  const getElections = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const elections = await contractService.getElections();
      setState(prev => ({ ...prev, elections: elections as Election[], loading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // إنشاء انتخابات جديدة
  const createElection = async (title: string, startTime: number, endTime: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const electionId = await contractService.createElection(title, startTime, endTime);
      console.log('Created election with ID:', electionId);
      await getElections(); // تحديث قائمة الانتخابات بعد الإنشاء
      setState(prev => ({ ...prev, loading: false }));
      return electionId;
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // إضافة مرشح جديد
  const addCandidate = async (electionId: number, candidateId: number, name: string, description: string, imageUrl: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.addCandidate(electionId, candidateId, name, description, imageUrl);
      await getElections(); // تحديث قائمة الانتخابات بعد الإضافة
      setState(prev => ({ ...prev, loading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // إزالة مرشح
  const removeCandidate = async (electionId: number, candidateId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.removeCandidate(electionId, candidateId);
      await getElections(); // تحديث قائمة الانتخابات بعد الإزالة
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // التصويت لمرشح
  const vote = async (electionId: number, candidateId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.vote(electionId, candidateId);
      await getElections(); // تحديث قائمة الانتخابات بعد التصويت
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // التسجيل الذاتي للناخب
  const selfRegisterVoter = async (electionId: number, nationalId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.selfRegisterVoter(electionId, nationalId);
      setState(prev => ({ ...prev, loading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // تسجيل ناخب من قبل المسؤول
  const registerVoter = async (electionId: number, voterAddress: string, nationalId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.registerVoter(electionId, voterAddress, nationalId);
      setState(prev => ({ ...prev, loading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // التحقق من تسجيل الناخب
  const isVoterRegistered = async (electionId: number, voterAddress: string) => {
    try {
      return await contractService.isVoterRegistered(electionId, voterAddress);
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message }));
      throw error;
    }
  };

  // التحقق من تصويت الناخب
  const hasVoted = async (electionId: number, voterAddress: string) => {
    try {
      return await contractService.hasVoted(electionId, voterAddress);
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message }));
      throw error;
    }
  };

  // إيقاف انتخابات
  const pauseElection = async (electionId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.pauseElection(electionId);
      await getElections(); // تحديث قائمة الانتخابات بعد الإيقاف
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // استئناف انتخابات
  const resumeElection = async (electionId: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.resumeElection(electionId);
      await getElections(); // تحديث قائمة الانتخابات بعد الاستئناف
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // الحصول على نتائج الانتخابات - استخدام useCallback لمنع إعادة الإنشاء في كل تحديث
  const getElectionResults = useCallback(async (electionId: number) => {
    try {
      // تعيين حالة التحميل إلى true في البداية
      setState(prev => ({ ...prev, loading: true, error: null }));

      // جلب النتائج من العقد الذكي
      const results = await contractService.getElectionResults(electionId);

      // إضافة تأخير زمني لإظهار حالة التحميل (6 ثوانٍ)
      await new Promise(resolve => setTimeout(resolve, 6000));

      // تعيين حالة التحميل إلى false بعد الانتهاء
      setState(prev => ({ ...prev, loading: false }));

      return results;
    } catch (error: any) {
      // إضافة تأخير زمني حتى في حالة الخطأ (1 ثانية)
      await new Promise(resolve => setTimeout(resolve, 1000));

      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  }, [setState]);

  // تحديث تفاصيل الانتخابات
  const updateElectionDetails = async (electionId: number, title: string, startTime: number, endTime: number) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.updateElectionDetails(electionId, title, startTime, endTime);
      await getElections(); // تحديث قائمة الانتخابات بعد التعديل
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // تغيير المسؤول
  const changeAdmin = async (newAdminAddress: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await contractService.changeAdmin(newAdminAddress);
      setState(prev => ({ ...prev, loading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
      throw error;
    }
  };

  // الاستماع للأحداث
  const listenForEvents = (callback: (eventName: string, data: any) => void) => {
    return contractService.listenForEvents(callback);
  };

  // التحقق من وجود المحفظة عند تحميل التطبيق
  useEffect(() => {
    const checkWallet = async () => {
      if (window.ethereum) {
        try {
          // الاستماع لتغييرات الحساب
          window.ethereum.on('accountsChanged', () => {
            // إعادة الاتصال بالمحفظة عند تغيير الحساب
            connectWallet().catch(console.error);
          });

          // الاستماع لتغييرات الشبكة
          window.ethereum.on('chainChanged', () => {
            // إعادة تحميل الصفحة عند تغيير الشبكة
            window.location.reload();
          });

          // التحقق من وجود حساب متصل بالفعل
          const accounts = await window.ethereum.request({ method: 'eth_accounts' });
          if (accounts.length > 0) {
            await connectWallet();
          }
        } catch (error) {
          console.error('خطأ في التحقق من المحفظة:', error);
        }
      }
    };

    checkWallet();

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      if (window.ethereum) {
        window.ethereum.removeAllListeners('accountsChanged');
        window.ethereum.removeAllListeners('chainChanged');
      }
    };
  }, []);

  // جلب قائمة الانتخابات عند تحميل التطبيق
  useEffect(() => {
    getElections().catch(console.error);
  }, []);

  // قيمة سياق التطبيق
  const contextValue: AppContextType = {
    state,
    connectWallet,
    disconnectWallet,
    getElections,
    getElectionResults,
    createElection,
    updateElectionDetails,
    addCandidate,
    removeCandidate,
    vote,
    selfRegisterVoter,
    registerVoter,
    isVoterRegistered,
    hasVoted,
    pauseElection,
    resumeElection,
    changeAdmin,
    listenForEvents
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// هوك استخدام سياق التطبيق
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext يجب استخدامه داخل AppProvider');
  }
  return context;
};


