import React, { useEffect } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { WalletConnect } from '../components/auth';
import { Link } from 'react-router-dom';
import FeatureSlider from '../components/FeatureSlider';
import './HomePage.css';

export const HomePage: React.FC = () => {
  const { state } = useAppContext();

  // إضافة تأثيرات الحركة عند تحميل الصفحة
  useEffect(() => {
    // يمكن إضافة تأثيرات إضافية هنا إذا لزم الأمر
    document.title = 'نظام التصويت الآمن بتقنية البلوكتشين';
  }, []);

  return (
    <div className="home-container">
      {/* قسم الترحيب */}
      <section className="hero-section animate-fadeInUp">
        <div className="hero-content">
          <h1 className="hero-title">نظام التصويت الآمن بتقنية البلوكتشين</h1>
          <p className="hero-description">
            نظام تصويت إلكتروني  يستخدم تقنية البلوكتشين لضمان انتخابات نزيهة
          </p>
        </div>
      </section>

      {/* قسم المميزات */}
      <section className="animate-fadeInUp delay-100">
        <FeatureSlider />
      </section>

      {/* قسم الاتصال بالمحفظة */}
      <section className="animate-fadeInUp delay-200">
        <div className="connect-section">
          <WalletConnect />

          {state.user && (
            <div className="success-message">
              <p className="font-medium">أنت متصل الآن!</p>
              <p className="text-sm">يمكنك الآن الانتقال إلى صفحة الانتخابات للتصويت أو عرض النتائج</p>

              <div className="action-buttons">
                <Link
                  to="/elections"
                  className="action-button action-button-primary"
                >
                  الانتخابات
                </Link>

                {state.user.isAdmin && (
                  <Link
                    to="/admin"
                    className="action-button action-button-secondary"
                  >
                    لوحة التحكم
                  </Link>
                )}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* قسم خطوات الاستخدام */}
      <section className="steps-section animate-fadeInUp delay-300">
        <h2 className="steps-title">كيفية استخدام النظام</h2>

        <div className="steps-grid">
          <div className="step-card">
            <div className="step-icon bg-blue-100">
              <span className="step-number text-blue-600">1</span>
            </div>
            <h3 className="step-title">اتصل بمحفظتك</h3>
            <p className="step-description">
              قم بتوصيل محفظة MetaMask  للوصول إلى النظام
            </p>
          </div>

          <div className="step-card">
            <div className="step-icon bg-green-100">
              <span className="step-number text-green-600">2</span>
            </div>
            <h3 className="step-title">سجل كناخب</h3>
            <p className="step-description">
              قم بتسجيل نفسك كناخب في الانتخابات التي ترغب في المشاركة فيها
            </p>
          </div>

          <div className="step-card">
            <div className="step-icon bg-purple-100">
              <span className="step-number text-purple-600">3</span>
            </div>
            <h3 className="step-title">صوت بأمان</h3>
            <p className="step-description">
              اختر المرشح الذي تريد التصويت له وقم بتأكيد التصويت من خلال محفظتك
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

