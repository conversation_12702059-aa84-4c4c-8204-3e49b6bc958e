import React, { useState, useEffect, useRef } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { BarChart3, Trophy, Medal } from 'lucide-react';
import { Candidate } from '../../interfaces';

interface ElectionResultsProps {
  electionId: number;
}

export const ElectionResults: React.FC<ElectionResultsProps> = ({ electionId }) => {
  const { getElectionResults, state } = useAppContext();
  const [results, setResults] = useState<Candidate[]>([]);

  // استخدام حالة التحميل والخطأ من سياق التطبيق
  const loading = state.loading;
  const error = state.error;

  const election = state.elections.find(e => e.id === electionId);

  // استخدام useRef لتتبع ما إذا كان قد تم تحميل النتائج بالفعل
  const resultsLoaded = useRef(false);

  // استخدام useRef لتتبع ما إذا كان طلب الحصول على النتائج قيد التنفيذ
  const isFetchingRef = useRef(false);

  useEffect(() => {
    // تحميل النتائج مرة واحدة فقط عند تحميل المكون أو تغيير معرف الانتخابات
    // والتأكد من عدم وجود طلب قيد التنفيذ حالياً
    if ((!resultsLoaded.current || results.length === 0) && !isFetchingRef.current && !loading) {
      const fetchResults = async () => {
        try {
          // تعيين حالة الطلب إلى قيد التنفيذ
          isFetchingRef.current = true;

          // الحصول على النتائج
          const data = await getElectionResults(electionId);

          // ترتيب النتائج تنازلياً حسب عدد الأصوات
          const sortedResults = [...data].sort((a, b) => b.voteCount - a.voteCount);
          setResults(sortedResults);
          resultsLoaded.current = true;
        } catch (err: any) {
          console.error('Error fetching election results:', err);
        } finally {
          // إعادة تعيين حالة الطلب بعد الانتهاء
          isFetchingRef.current = false;
        }
      };

      fetchResults();
    }
  }, [electionId, loading, getElectionResults]);

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-center text-gray-600">جاري تحميل نتائج الانتخابات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-center text-red-600">{error}</p>
      </div>
    );
  }

  if (!election) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-center text-gray-600">لم يتم العثور على الانتخابات</p>
      </div>
    );
  }

  // التحقق من انتهاء التصويت
  const now = Math.floor(Date.now() / 1000);
  const hasEnded = now > election.endTime;

  // حساب إجمالي الأصوات
  const totalVotes = results.reduce((sum, candidate) => sum + candidate.voteCount, 0);

  // إذا لم ينتهِ التصويت بعد، عرض رسالة مناسبة (إلا للمسؤول)
  if (!hasEnded && !state.user?.isAdmin) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">نتائج انتخابات: {election.title}</h2>
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="font-medium">لم ينتهِ التصويت بعد</p>
            <p className="text-sm">سيتم عرض النتائج بعد انتهاء التصويت في {new Date(election.endTime * 1000).toLocaleString('ar-SA')}</p>
          </div>
        </div>
      </div>
    );
  }

  // إذا كان المستخدم مسؤولاً ولم ينتهِ التصويت بعد، عرض تنبيه
  if (!hasEnded && state.user?.isAdmin) {
    return (
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold mb-4">نتائج انتخابات: {election.title}</h2>

          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="font-medium">عرض مبكر للنتائج (للمسؤول فقط)</p>
              <p className="text-sm">أنت تشاهد النتائج قبل انتهاء التصويت لأنك مسؤول. لن يتمكن الناخبون العاديون من رؤية هذه النتائج حتى {new Date(election.endTime * 1000).toLocaleString('ar-SA')}</p>
            </div>
          </div>

          {/* عرض النتائج للمسؤول */}
          <div className="mb-4">
            <p className="text-gray-600">إجمالي الأصوات حتى الآن: {totalVotes}</p>
          </div>

          {results.length === 0 ? (
            <p className="text-center text-gray-600">لا توجد أصوات حتى الآن</p>
          ) : (
            <div className="space-y-6">
              {/* عرض جميع النتائج */}
              <div>
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <BarChart3 className="w-6 h-6 ml-2" />
                  النتائج المؤقتة
                </h3>

                <div className="space-y-4">
                  {results.map((candidate) => (
                    <div key={candidate.id} className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="flex justify-between mb-2">
                        <h4 className="font-bold">{candidate.name}</h4>
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                          {candidate.voteCount} صوت
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${totalVotes > 0 ? (candidate.voteCount / totalVotes) * 100 : 0}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {totalVotes > 0
                          ? ((candidate.voteCount / totalVotes) * 100).toFixed(1)
                          : 0}%
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">نتائج انتخابات: {election.title}</h2>

        <div className="mb-4">
          <p className="text-gray-600">إجمالي الأصوات: {totalVotes}</p>
        </div>

        {results.length === 0 ? (
          <p className="text-center text-gray-600">لا توجد نتائج متاحة</p>
        ) : (
          <div className="space-y-6">
            {/* عرض الفائزين الثلاثة الأوائل */}
            {totalVotes > 0 && (
              <div className="mb-8">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <Trophy className="w-6 h-6 ml-2 text-yellow-500" />
                  المتصدرون
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {results.slice(0, 3).map((candidate, index) => (
                    <div key={candidate.id} className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-bold text-lg">{candidate.name}</h4>
                        {index === 0 ? (
                          <Medal className="w-6 h-6 text-yellow-500" />
                        ) : index === 1 ? (
                          <Medal className="w-6 h-6 text-gray-400" />
                        ) : index === 2 ? (
                          <Medal className="w-6 h-6 text-amber-700" />
                        ) : null}
                      </div>
                      <p className="text-gray-600 mb-2">الأصوات: {candidate.voteCount}</p>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${(candidate.voteCount / totalVotes) * 100}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {((candidate.voteCount / totalVotes) * 100).toFixed(1)}%
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* عرض جميع النتائج */}
            <div>
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <BarChart3 className="w-6 h-6 ml-2" />
                جميع النتائج
              </h3>

              <div className="space-y-4">
                {results.map((candidate) => (
                  <div key={candidate.id} className="bg-white border rounded-lg p-4 shadow-sm">
                    <div className="flex justify-between mb-2">
                      <h4 className="font-bold">{candidate.name}</h4>
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        {candidate.voteCount} صوت
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${(candidate.voteCount / totalVotes) * 100}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      {totalVotes > 0
                        ? ((candidate.voteCount / totalVotes) * 100).toFixed(1)
                        : 0}%
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
