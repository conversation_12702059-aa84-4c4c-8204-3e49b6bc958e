import { viem } from "hardhat";

async function main() {
  const publicClient = await viem.getPublicClient();
  const [deployer] = await viem.getWalletClients();
  
  console.log("Deploying contracts with the account:", deployer.account.address);
  
  const electionFactory = await viem.deployContract("Election");
    
  // حفظ عنوان العقد لاستخدامه في الواجهة الأمامية
  console.log("Election contract deployed to:", electionFactory.address);
  
  console.log("Contract address saved for frontend integration");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
