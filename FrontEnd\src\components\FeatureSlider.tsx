import React from 'react';
import Slider from 'react-slick';
import { Shield, Vote, BarChart3, Users, Zap } from 'lucide-react';

// تعريف نوع البيانات للميزة
interface Feature {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

const FeatureSlider: React.FC = () => {
  // قائمة مميزات النظام
  const features: Feature[] = [
    {
      id: 1,
      title: 'آمن وموثوق',
      description: 'يستخدم تقنية البلوكتشين لضمان أمان وشفافية العملية الانتخابية، مما يمنع التلاعب بالأصوات',
      icon: <Shield className="w-10 h-10" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      id: 2,
      title: 'سهل الاستخدام',
      description: 'واجهة مستخدم بسيطة وسهلة الاستخدام تتيح للناخبين التصويت بسهولة من أي مكان',
      icon: <Vote className="w-10 h-10" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      id: 3,
      title: 'نتائج فورية',
      description: 'عرض نتائج الانتخابات بشكل فوري وشفاف، مع إمكانية التحقق من صحة النتائج',
      icon: <BarChart3 className="w-10 h-10" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      id: 4,
      title: 'لامركزي بالكامل',
      description: 'نظام لامركزي بالكامل يعمل على شبكة الإيثريوم، مما يضمن عدم وجود نقطة فشل مركزية',
      icon: <Users className="w-10 h-10" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      id: 5,
      title: 'سرعة وكفاءة',
      description: 'معالجة سريعة للأصوات وتحديث فوري للنتائج مع الحفاظ على أمان وسلامة البيانات',
      icon: <Zap className="w-10 h-10" />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    }
  ];

  // إعدادات السلايدر
  const settings = {
    dots: true,
    infinite: true,
    speed: 450,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    rtl: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  return (
    <div className="feature-slider-container my-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">مميزات النظام</h2>

      <div className="px-4">
        <Slider {...settings}>
          {features.map((feature) => (
            <div key={feature.id} className="px-2">
              <div className="feature-card bg-white rounded-lg shadow-lg p-6 mx-2 my-4 h-64 flex flex-col items-center justify-center">
                <div className={`${feature.bgColor} p-4 rounded-full mb-4 ${feature.color}`}>
                  {feature.icon}
                </div>
                <h3 className="font-bold text-xl mb-2 text-center">{feature.title}</h3>
                <p className="text-gray-600 text-center">{feature.description}</p>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default FeatureSlider;
